import { request } from './request'
import axios from 'axios'

/**
 * 获取所有配置状态
 * @returns Promise
 */
export function getAllConfigStatus() {
  return request({
    url: '/api/config/status/all',
    method: 'get'
  }).then(res => {
    // 修复中文编码问题
    if (res.code === 200 && Array.isArray(res.data)) {
      res.data = res.data.map((item: ConfigItem) => {
        return {
          ...item,
          description: configDescriptions[item.configType] || item.description
        }
      })
    }
    return res
  })
}

/**
 * 获取配置信息
 * @param configType 配置类型
 * @returns Promise
 */
export function getConfig(configType: string) {
  if (import.meta.env.DEV) {
    console.debug('正在请求配置:', configType);  // 添加调试日志
  }

  // 使用正确的配置接口路径
  let url = `/api/config/type/${configType}`;

  // 对于特定的配置类型，使用专用的API路径
  if (configType === 'system-logo') {
    url = `/api/config/system-logo`;
  } else if (configType === 'system') {
    url = `/api/config/system`;
  }

  if (import.meta.env.DEV) {
    console.debug('请求URL:', url);  // 添加调试日志
  }

  return new Promise<any>(async (resolve, reject) => {
    try {
      // 尝试从API获取
      if (import.meta.env.DEV) {
        console.debug(`开始API请求: ${url}`);  // 添加调试日志
      }
      const response = await request({
        url: url,
        method: 'get'
      });

      if (import.meta.env.DEV) {
        console.debug('API响应:', response);  // 添加调试日志
      }
      
      // 如果API成功，缓存到本地存储
      if (response.code === 200 && response.data) {
        try {
          localStorage.setItem(`config_${configType}`, JSON.stringify(response.data.configValue || {}));
        } catch (storageError) {
          if (import.meta.env.DEV) {
            console.debug('无法将配置保存到本地存储:', storageError);
          }
        }
      }

      resolve(response);
    } catch (error: any) {  // 添加类型注解
      if (import.meta.env.DEV) {
        console.debug(`API获取${configType}配置失败:`, error);

        // 打印更详细的错误信息
        if (error.response) {
          console.debug('错误状态码:', error.response.status);
          console.debug('错误数据:', error.response.data);
        }
      }
      
      // 尝试从本地存储获取
      try {
        const localData = localStorage.getItem(`config_${configType}`);
        if (localData) {
          const parsedData = JSON.parse(localData);
          if (import.meta.env.DEV) {
            console.debug('从本地存储加载配置:', parsedData);
          }

          // 构造一个类似API响应的对象
          resolve({
            code: 200,
            message: '从本地存储加载',
            data: {
              configType,
              configValue: parsedData
            }
          });
          return;
        }
      } catch (localError) {
        if (import.meta.env.DEV) {
          console.debug('从本地存储获取配置失败:', localError);
        }
      }
      
      // 如果本地也没有，则返回空配置
      resolve({
        code: 200,
        message: '未找到配置，返回空对象',
        data: {
          configType,
          configValue: {}
        }
      });
    }
  });
}

/**
 * 保存配置信息
 * @param configType 配置类型
 * @param data 配置数据
 * @returns Promise
 */
export function saveConfig(configType: string, data: any) {
  return new Promise<any>(async (resolve, reject) => {
    // 先保存到本地存储
    try {
      localStorage.setItem(`config_${configType}`, JSON.stringify(data || {}));
      if (import.meta.env.DEV) {
        console.debug(`已将${configType}配置保存到本地存储`);
      }
    } catch (storageError) {
      if (import.meta.env.DEV) {
        console.debug('无法将配置保存到本地存储:', storageError);
      }
    }

    try {
      // 尝试保存到API
      if (import.meta.env.DEV) {
        console.debug(`向 ${configType} 发送数据:`, data);
      }

      // 使用正确的配置接口路径
      let url = `/api/config/type/${configType}`;

      // 对于特定的配置类型，使用专用的API路径
      if (configType === 'system-logo') {
        url = `/api/config/system-logo`;
      } else if (configType === 'system') {
        url = `/api/config/system`;
      }

      const response = await request({
        url: url,
        method: 'post',
        data
      });

      if (import.meta.env.DEV) {
        console.debug(`API返回结果:`, response);
      }
      
      // 确保返回成功代码
      if (response && !response.code) {
        response.code = 200;
      }
      
      resolve(response);
    } catch (error) {
      if (import.meta.env.DEV) {
        console.debug(`API保存${configType}配置失败:`, error);
      }

      // 如果API保存失败，返回本地保存成功的消息
      resolve({
        code: 200,
        message: '配置已保存到本地存储',
        data: {
          configType,
          configValue: data
        }
      });
    }
  });
}

/**
 * 测试配置连接
 * @param configType 配置类型
 * @param data 配置数据
 * @returns Promise
 */
export function testConfig(configType: string, data: any) {
  return request({
    url: `/api/config/${configType}/test`,
    method: 'post',
    data
  })
}

// 配置类型枚举
export enum ConfigType {
  WECHAT_OFFICIAL = 'wechat-official',
  WECHAT_MINIPROGRAM = 'wechat-miniprogram',
  WECHAT_PAY = 'wechat-pay',
  ALIPAY = 'alipay',
  KOUZI = 'kouzi',
  VOLCANO_ARK = 'volcano-ark',
  FREE_QUOTA = 'free-quota',
  REFERRAL = 'referral',
  SYSTEM = 'system'
}

// 配置项接口
export interface ConfigItem {
  configType: string;
  description: string;
  isConfigured: boolean;
  isActive: boolean;
  lastTestResult: boolean | null;
  lastTestTime: string | null;
  configValue?: Record<string, any>;
}

// 配置描述映射
const configDescriptions: Record<string, string> = {
  [ConfigType.WECHAT_OFFICIAL]: '微信公众号配置',
  [ConfigType.WECHAT_MINIPROGRAM]: '微信小程序配置',
  [ConfigType.WECHAT_PAY]: '微信支付配置',
  [ConfigType.ALIPAY]: '支付宝配置',
  [ConfigType.KOUZI]: '扣子服务配置',
  [ConfigType.VOLCANO_ARK]: '火山方舟配置',
  [ConfigType.FREE_QUOTA]: '免费额度配置',
  [ConfigType.REFERRAL]: '拉新配置',
  [ConfigType.SYSTEM]: '系统配置'
}

/**
 * 上传文件
 * @param url 上传地址
 * @param formData FormData对象，包含文件和其他参数
 * @returns Promise
 */
export function uploadFile(url: string, formData: FormData) {
  return new Promise<any>((resolve, reject) => {
    axios.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        // 使用本地存储的令牌
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
      }
    })
      .then(response => {
        resolve(response.data)
      })
      .catch(error => {
        if (import.meta.env.DEV) {
          console.debug('文件上传失败:', error)
        }
        reject(error)
      })
  })
}

/**
 * 获取客服图片配置
 * @returns Promise
 */
export function getCustomerServiceImage() {
  return getConfig('system-logo').then(res => {
    if (res && res.data && res.data.configValue) {
      return {
        code: 200,
        data: {
          customerServiceImage: res.data.configValue.customerServiceImage || ''
        }
      };
    }
    return {
      code: 200,
      data: {
        customerServiceImage: ''
      }
    };
  });
}

// 默认导出，包含所有配置相关的函数
const configApi = {
  getAllConfigStatus,
  getConfig,
  saveConfig,
  testConfig,
  uploadFile,
  getCustomerServiceImage,
  ConfigType,
  // 为了兼容性，添加一些别名
  getConfigByType: getConfig,
  saveConfigByType: saveConfig,
  testConfigByType: testConfig,
  // 添加updateConfig方法，实际调用saveConfig
  updateConfig: (configType: string, data: any) => {
    if (import.meta.env.DEV) {
      console.debug(`调用updateConfig方法: ${configType}`, data);
    }
    return saveConfig(configType, data);
  },
  // 添加resetConfig方法
  resetConfig: (configType: string) => {
    if (import.meta.env.DEV) {
      console.debug(`调用resetConfig方法: ${configType}`);
    }
    return saveConfig(configType, {});
  },
  // 添加清除缓存方法
  clearCache: () => {
    if (import.meta.env.DEV) {
      console.debug('调用clearCache方法');
    }
    return request({
      url: '/api/system/clear-cache',
      method: 'post'
    });
  }
}

export default configApi