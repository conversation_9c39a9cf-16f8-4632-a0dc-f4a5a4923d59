const jwt = require('jsonwebtoken');
const config = require('../config');
const { getConnection } = require('../db/database');

/**
 * Authentication Middleware
 * Verifies JWT token from Authorization header
 */
module.exports = async (req, res, next) => {
  // Public paths that don't require authentication
  const publicPaths = [
    '/api/auth/login',
    '/api/auth/register',
    '/api/health',
    '/health',
    '/api/system/status',
    '/public'
  ];
  
  // Skip authentication for public paths
  if (publicPaths.some(path => req.path.startsWith(path))) {
    return next();
  }
  
  // Get token from header
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // Format: "Bearer TOKEN"
  
  if (!token) {
    return res.status(401).json({
      success: false,
      message: '未提供访问令牌'
    });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, config.jwtSecret);
    
    // Get database connection
    const connection = await getConnection();
    if (!connection) {
      return res.status(500).json({
        success: false,
        message: '数据库连接失败'
      });
    }
    
    // Check if user exists
    const user = await connection.query(
      'SELECT * FROM user WHERE id = ?',
      [decoded.id]
    );

    if (user.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 允许所有已认证的用户访问
    console.log(`用户访问API: ID=${user[0].id}, 用户名=${user[0].username}`);
    
    // Add user data to request
    req.user = user[0];
    
    next();
  } catch (err) {
    console.error('验证令牌失败:', err);
    return res.status(401).json({
      success: false,
      message: '无效的访问令牌'
    });
  }
}; 