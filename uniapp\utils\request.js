/**
 * 网络请求工具类
 */
import { API_BASE_URL, IMAGE_BASE_URL } from '../config/index.js'

/**
 * 封装的网络请求方法
 * @param {Object} options - 请求配置
 * @returns {Promise} Promise对象
 */
export const request = (options = {}) => {
  return new Promise((resolve, reject) => {
    // 默认配置
    const defaultOptions = {
      url: '',
      method: 'GET',
      data: {},
      header: {},
      loading: true
    }
    
    // 合并配置
    const mergeOptions = {
      ...defaultOptions,
      ...options
    }
    
    // 如果URL不是以http开头，添加基础URL
    if (!mergeOptions.url.startsWith('http')) {
      mergeOptions.url = API_BASE_URL + mergeOptions.url
    }
    
    // 显示加载提示
    if (mergeOptions.loading) {
      uni.showLoading({
        title: '加载中...',
        mask: true
      })
    }
    
    // 发送请求
    uni.request({
      ...mergeOptions,
      success: (res) => {
        try {
          let responseData = res.data;

          // 检查HTTP状态码
          if (res.statusCode >= 200 && res.statusCode < 300) {
            // 成功响应
            if (responseData && responseData.code !== undefined) {
              // 正常API响应
              resolve(responseData);
            } else {
              // 处理直接返回的数组或对象
              resolve(responseData);
            }
          } else {
            // HTTP错误状态码
            console.error('HTTP错误状态码:', res.statusCode, responseData);

            // 构造错误对象
            const error = new Error(responseData?.message || `HTTP ${res.statusCode} 错误`);
            error.statusCode = res.statusCode;
            error.data = responseData;

            reject(error);
          }
        } catch(e) {
          console.error('处理响应数据错误:', e);
          reject(e);
        }
      },
      fail: (err) => {
        uni.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
        console.error('请求失败:', err)
        reject(err)
      },
      complete: () => {
        if (mergeOptions.loading) {
          uni.hideLoading()
        }
      }
    })
  })
}

/**
 * 获取完整图片URL
 * @param {String} imagePath - 图片路径
 * @returns {String} 完整的图片URL
 */
export const getImageUrl = (imagePath) => {
  if (!imagePath) return ''
  
  // 如果图片路径已经包含完整URL则直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath
  }
  
  // 否则拼接图片服务的基础URL
  return IMAGE_BASE_URL + (imagePath.startsWith('/') ? '' : '/') + imagePath
}

export default {
  request,
  getImageUrl
} 