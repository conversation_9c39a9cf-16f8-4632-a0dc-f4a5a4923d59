import App from './App'
import config, { isDevelopment, enableConsoleLog } from './config/index.js'
import request from './utils/request.js'

// 开发模式控制台日志管理

// 控制台日志管理器
const consoleManager = {
	originalConsole: {
		log: console.log,
		warn: console.warn,
		error: console.error,
		info: console.info,
		debug: console.debug
	},

	// 启用控制台日志
	enable() {
		Object.keys(this.originalConsole).forEach(method => {
			console[method] = this.originalConsole[method]
		})
	},

	// 禁用控制台日志（保留error）
	disable() {
		console.log = () => {}
		console.warn = () => {}
		console.info = () => {}
		console.debug = () => {}
		console.trace = () => {}
		console.group = () => {}
		console.groupEnd = () => {}
		console.groupCollapsed = () => {}
		console.table = () => {}
		console.time = () => {}
		console.timeEnd = () => {}
		console.count = () => {}
		console.clear = () => {}
		// 保留error日志，因为错误信息很重要
		// console.error = () => {}
	}
}

// 立即应用日志配置（在任何其他代码执行之前）
if (!enableConsoleLog) {
	// 立即禁用所有日志
	const noop = () => {}
	console.log = noop
	console.warn = noop
	console.info = noop
	console.debug = noop
	console.trace = noop
	console.group = noop
	console.groupEnd = noop
	console.groupCollapsed = noop
	console.table = noop
	console.time = noop
	console.timeEnd = noop
	console.count = noop
	console.clear = noop
}

// 根据配置决定是否启用控制台日志
if (enableConsoleLog) {
	consoleManager.enable()
	console.log('🔧 开发模式')
} else {
	consoleManager.disable()
	// 使用原始console.log来显示这条消息，然后立即禁用
	consoleManager.originalConsole.log('💡')
}

// 控制台管理器仅用于启动时配置，不需要运行时控制

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

// 挂载全局配置和方法
Vue.prototype.$config = config
Vue.prototype.$api = request.request
Vue.prototype.$getImageUrl = request.getImageUrl

Vue.config.productionTip = false

// 禁用Vue的一些开发警告（可选）
Vue.config.silent = !enableConsoleLog
Vue.config.devtools = isDevelopment && enableConsoleLog
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  
  // 挂载全局配置和方法
  app.config.globalProperties.$config = config
  app.config.globalProperties.$api = request.request
  app.config.globalProperties.$getImageUrl = request.getImageUrl
  
  return {
    app
  }
}
// #endif