<template>
	<view class="container">

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- AI助手卡片 -->
			<view class="ai-card">
				<view class="ai-avatar-container">
					<image class="ai-avatar" :src="agentAvatar" mode="aspectFill"></image>
				</view>
			</view>

			<!-- 欢迎消息 -->
			<view class="welcome-message">
				<text class="welcome-title">{{ welcomeTitle }}</text>
				<text class="welcome-desc">{{ welcomeDesc }}</text>
			</view>


			<!-- 多媒体内容卡片 -->
			<view class="media-cards">
				<!-- 案例视频展示 -->
				<view v-if="mediaContents.video.enabled" class="media-section">
					<view class="media-card video-card">
						<view class="media-header">
							<view class="media-type-icon">
								<text class="type-icon">📹</text>
							</view>
							<text class="media-title">{{ mediaContents.video.title }}</text>
						</view>
						<view class="image-container">
							<video
								class="media-image"
								:src="mediaContents.video.content"
								controls
								show-center-play-btn
								show-play-btn
								object-fit="fill"
								:style="{
									width: '100%',
									height: videoHeight + 'px'
								}"
								@loadedmetadata="onVideoLoaded"
								@timeupdate="onVideoTimeUpdate"
								@canplay="onVideoCanPlay"
								@error="onVideoError"
							></video>
						</view>
					</view>
				</view>

				<!-- 案例图片展示 -->
				<view v-if="mediaContents.image.enabled" class="media-section">
					<view class="media-card image-card">
						<view class="media-header">
							<view class="media-type-icon">
								<text class="type-icon">🖼️</text>
							</view>
							<text class="media-title">{{ mediaContents.image.title }}</text>
						</view>
						<view class="image-container">
							<image
								class="media-image"
								:src="mediaContents.image.content"
								mode="widthFix"
								@load="onImageLoaded"
							></image>
						</view>
					</view>
				</view>

				<!-- 文字教学 -->
				<view v-if="mediaContents.text.enabled" class="media-section">
					<view class="media-card text-card">
						<view class="media-header">
							<view class="media-type-icon">
								<text class="type-icon">📄</text>
							</view>
							<text class="media-title">{{ mediaContents.text.title }}</text>
						</view>
						<view class="text-content-wrapper">
							<text class="text-teaching-content">{{ mediaContents.text.content }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 开始聊天按钮和功能按钮组 -->
		<view class="start-chat-container">
			<!-- 历史记录按钮 -->
			<view class="function-btn history-btn" @click="openHistoryModal">
				<view class="btn-icon">📖</view>
			</view>

			<!-- 开始工作流按钮 -->
			<view class="start-chat-btn" @click="openWorkflowModal">
				<view class="chat-icon">⚡</view>
				<text class="chat-text">执行工作流</text>
			</view>

			<!-- 分享按钮 -->
			<view class="function-btn share-btn" @click="openShareModal">
				<view class="btn-icon">📤</view>
			</view>
		</view>



		<!-- 历史记录弹窗 -->
		<view v-if="showHistoryModal" class="modal-overlay" @click="closeHistoryModal">
			<view class="history-modal" @click.stop>
				<!-- 弹窗头部 -->
				<view class="modal-header">
					<text class="modal-title">工作流历史</text>
					<view class="close-btn" @click="closeHistoryModal">✕</view>
				</view>

				<!-- 历史记录列表 -->
				<view class="history-content">
					<view v-if="historyLoading" class="loading-container">
						<text class="loading-text">加载中...</text>
					</view>

					<view v-else-if="historyList.length === 0" class="empty-container">
						<text class="empty-text">当前无工作流执行记录</text>
					</view>

					<view v-else class="history-list">
						<view
							v-for="item in historyList"
							:key="item.id"
							class="history-item"
						>
							<view class="history-time">{{ formatTime(item.createTime) }}</view>
							<view class="history-preview">
								<text class="workflow-title">{{ item.title }}</text>
								<text class="workflow-status">{{ item.statusText }}</text>
								<text class="workflow-consumption">消耗：{{ getWorkflowConsumption() }}点</text>

								<!-- 输入内容（只显示值，不显示参数名） -->
								<view v-if="getInputValues(item.input)" class="input-section">
									<text class="section-title">输入：</text>
									<text class="input-content">{{ getInputValues(item.input) }}</text>
								</view>

								<!-- 输出链接（只显示双引号内的链接） -->
								<view v-if="getOutputLinks(item.output).length > 0" class="output-section">
									<text class="section-title">输出：</text>
									<view class="output-links">
										<view
											v-for="(link, linkIndex) in getOutputLinks(item.output)"
											:key="linkIndex"
											class="output-link-item"
										>
											<text class="link-url">{{ truncateLink(link) }}</text>
											<button
												@click="copyLink(link)"
												:class="['copy-link-btn-text', { 'copied': copiedLinks.has(link) }]"
											>
												{{ copiedLinks.has(link) ? '已复制' : '复制链接' }}
											</button>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 加载更多 -->
					<view v-if="hasMoreHistory" class="load-more" @click="loadMoreHistory">
						<text class="load-more-text">加载更多</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 分享弹窗 -->
		<view v-if="showShareModal" class="modal-overlay" @click="closeShareModal">
			<view class="share-modal" @click.stop>
				<!-- 弹窗头部 -->
				<view class="modal-header">
					<text class="modal-title">分享智能体</text>
					<view class="close-btn" @click="closeShareModal">✕</view>
				</view>

				<!-- 分享内容 -->
				<view class="share-content">
					<view class="share-info">
						<view class="agent-info">
							<image class="agent-avatar" :src="agentInfo.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
							<view class="agent-details">
								<text class="agent-name">{{ agentInfo.name }}</text>
								<text class="agent-desc">{{ agentInfo.description }}</text>
							</view>
						</view>
					</view>

					<!-- 分享方式 -->
					<view class="share-methods">
						<!-- 微信分享 -->
						<view class="share-method" @click="shareToWechat">
							<view class="method-icon">💬</view>
							<text class="method-text">微信分享</text>
						</view>

						<!-- 生成邀请码 -->
						<view class="share-method" @click="generateShareCode">
							<view class="method-icon">🔗</view>
							<text class="method-text">生成邀请码</text>
						</view>

						<!-- 复制链接 -->
						<view class="share-method" @click="copyShareLink">
							<view class="method-icon">📋</view>
							<text class="method-text">复制链接</text>
						</view>
					</view>

					<!-- 邀请码显示 -->
					<view v-if="shareCode" class="share-code-container">
						<text class="share-code-label">邀请码：</text>
						<view class="share-code-box">
							<text class="share-code">{{ shareCode }}</text>
							<view class="copy-code-btn" @click="copyShareCode">复制</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 工作流执行弹窗 -->
		<view v-if="showWorkflowModal" class="chat-modal-overlay" @click="onModalOverlayClick">
			<view class="chat-modal" @click.stop>
				<!-- 弹窗头部 -->
				<view class="modal-header">
					<view class="header-info">
						<view class="points-info">
							<text class="points-text">当前剩余点数：{{ remainingPoints || 0 }}</text>
							<text class="consumption-text">工作流执行扣除点数：{{ currentTheme?.consumption || workflowConsumption || 1000 }}</text>
						</view>
					</view>
					<view class="close-btn" @click="closeWorkflowModal">
						<text class="close-icon">✕</text>
					</view>
				</view>

				<!-- 工作流参数输入区域 -->
				<view class="modal-chat-container">

					<!-- 工作流参数输入表单 -->
					<view class="workflow-params-container">
						<view class="params-form">
							<view
								v-for="param in workflowParams"
								:key="param.name"
								class="param-item"
							>
								<view class="param-label">
									<text class="label-text">{{ param.label || param.tag || param.name }}</text>
									<text v-if="param.required" class="required-mark">*</text>
								</view>
								<view class="param-description" v-if="param.description">
									<text class="description-text">{{ param.description }}</text>
								</view>
								<view class="param-input">
									<!-- 单行文本输入 -->
									<input
										v-if="param.type === 'string'"
										type="text"
										class="text-input"
										:placeholder="param.placeholder || `请输入${param.label || param.tag || param.name}`"
										v-model="workflowInputs[param.name]"
										@input="onWorkflowInputChange(param.name, $event)"
									/>

									<!-- 多行文本输入 -->
									<textarea
										v-else-if="param.type === 'text'"
										class="textarea-input"
										:placeholder="param.placeholder || `请输入${param.label || param.tag || param.name}`"
										:data-param="param.name"
										:ref="`textarea_${param.name}`"
										v-model="workflowInputs[param.name]"
										@input="onWorkflowInputChange(param.name, $event)"
										@click="onInputClick(param.name)"
									></textarea>

									<!-- 单选气泡按钮组 (适用于 select 和 radio 类型) -->
									<view
										v-else-if="(param.type === 'select' || param.type === 'radio') && param.options"
										class="bubble-button-group"
									>
										<view
											v-for="option in param.options"
											:key="option"
											class="bubble-button"
											:class="{ 'bubble-button-selected': workflowInputs[param.name] === option }"
											@click="onBubbleButtonClick(param.name, option)"
										>
											<text class="bubble-button-text">{{ option }}</text>
										</view>
									</view>

									<!-- 多选复选框组 -->
									<view
										v-else-if="param.type === 'checkbox' && param.options"
										class="checkbox-button-group"
									>
										<view
											v-for="option in param.options"
											:key="option"
											class="checkbox-button"
											:class="{ 'checkbox-button-active': isCheckboxChecked(param.name, option) }"
											@click="onCheckboxButtonClick(param.name, option)"
										>
											<text class="checkbox-button-text">{{ option }}</text>
										</view>
									</view>

									<!-- 图片上传 -->
									<view
										v-else-if="param.type === 'image'"
										class="upload-container"
									>
										<view class="upload-preview" v-if="workflowInputs[param.name]">
											<image
												:src="workflowInputs[param.name]"
												class="preview-image"
												mode="aspectFit"
												@click="previewUploadedImage(workflowInputs[param.name])"
											></image>
											<view class="remove-btn" @click="removeUploadedFile(param.name)">×</view>
										</view>
										<view
											v-else
											class="upload-btn"
											@click="uploadImage(param.name)"
										>
											<text class="upload-icon">📷</text>
											<text class="upload-text">上传图片</text>
										</view>
									</view>

									<!-- 视频上传 -->
									<view
										v-else-if="param.type === 'video'"
										class="upload-container"
									>
										<view class="upload-preview" v-if="workflowInputs[param.name]">
											<video
												:src="workflowInputs[param.name]"
												class="preview-video"
												controls
												show-center-play-btn
											></video>
											<view class="remove-btn" @click="removeUploadedFile(param.name)">×</view>
										</view>
										<view
											v-else
											class="upload-btn"
											@click="uploadVideo(param.name)"
										>
											<text class="upload-icon">🎬</text>
											<text class="upload-text">上传视频</text>
										</view>
									</view>

									<!-- 默认处理（调试用） -->
									<view v-else class="debug-param">
										<text style="color: red; font-size: 12px;">
											未知参数类型: {{ param.type }} ({{ param.name }})
										</text>
										<text style="color: blue; font-size: 12px;">
											选项: {{ param.options }}
										</text>
									</view>
								</view>
							</view>
						</view>

						<!-- 工作流执行结果显示 -->
						<view v-if="workflowResult" class="workflow-result">
							<view class="result-header">
								<text class="result-title">执行结果</text>
							</view>
							<view class="result-content">
								<!-- 如果是链接数组，显示多个链接 -->
								<view v-if="Array.isArray(workflowResult)" class="links-container">
									<view
										v-for="(link, index) in workflowResult"
										:key="index"
										class="link-item"
									>
										<view class="link-content">
											<text class="link-url">{{ link }}</text>
										</view>
										<button @click="copyLink(link)" class="copy-link-btn">
											<text class="copy-icon">📋</text>
											<text class="copy-text">复制链接</text>
										</button>
									</view>
								</view>

								<!-- 如果是单个图片链接 -->
								<view v-else-if="isImageUrl(workflowResult)" class="media-container">
									<view class="image-wrapper">
										<!-- 图片加载状态 -->
										<view v-if="imageLoadingState === 'loading'" class="image-loading">
											<text class="loading-text">🖼️ 图片加载中...</text>
										</view>

										<!-- 图片加载失败状态 -->
										<view v-else-if="imageLoadingState === 'error'" class="image-error">
											<text class="error-icon">❌</text>
											<text class="error-text">图片加载失败</text>
											<text class="error-url">{{ workflowResult }}</text>
											<button @click="retryImageLoad" class="retry-btn">重试</button>
										</view>

										<!-- 正常显示图片 -->
										<view v-else class="image-container">
											<image
												:src="workflowResult"
												mode="aspectFit"
												class="result-image"
												@load="onImageLoad"
												@error="onImageLoadError"
												@click="previewImage(workflowResult)"
											/>
											<view class="image-overlay" @click="previewImage(workflowResult)">
												<text class="preview-icon">🔍</text>
												<text class="preview-text">点击预览</text>
											</view>
										</view>
									</view>

									<view class="media-actions">
										<button @click="previewImage(workflowResult)" class="action-btn preview-btn">🔍 预览</button>
										<button @click="saveMedia(workflowResult, 'image')" class="action-btn save-btn">💾 保存</button>
										<button @click="copyLink(workflowResult)" class="action-btn copy-btn">📋 复制</button>
									</view>
								</view>

								<!-- 如果是视频链接 -->
								<view v-else-if="isVideoUrl(workflowResult)" class="media-container">
									<video :src="workflowResult" controls class="result-video" @error="onVideoError"></video>
									<view class="media-actions">
										<button @click="saveMedia(workflowResult, 'video')" class="action-btn save-btn">保存视频</button>
										<button @click="copyLink(workflowResult)" class="action-btn copy-btn">复制链接</button>
									</view>
								</view>

								<!-- 如果是普通链接 -->
								<view v-else-if="isUrl(workflowResult)" class="link-container">
									<view class="link-preview" @click="openLink(workflowResult)">
										<text class="link-text">🔗 {{ workflowResult }}</text>
									</view>
									<view class="media-actions">
										<button @click="openLink(workflowResult)" class="action-btn open-btn">打开链接</button>
										<button @click="copyLink(workflowResult)" class="action-btn copy-btn">复制链接</button>
									</view>
								</view>

								<!-- 如果是普通文本 -->
								<view v-else class="text-container">
									<text class="result-text">{{ workflowResult }}</text>
									<view class="media-actions">
										<button @click="copyText(workflowResult)" class="action-btn copy-btn">复制内容</button>
									</view>
								</view>
							</view>
						</view>

						<!-- 工作流执行中的提示 -->
						<view v-if="isExecutingWorkflow" class="workflow-executing">
							<view class="executing-indicator">
								<view class="loading-dots">
									<view class="dot"></view>
									<view class="dot"></view>
									<view class="dot"></view>
								</view>
								<text class="executing-text">工作流执行中...</text>
							</view>
						</view>
					</view>

				</view>

				<!-- 工作流执行按钮区域 -->
				<view class="modal-input-container">
					<view class="workflow-action-wrapper">
						<!-- 停止按钮（在执行工作流时显示） -->
						<view
							v-if="isExecutingWorkflow"
							class="modal-stop-btn"
							@click="stopWorkflowExecution"
						>
							<text class="stop-icon">⏹</text>
							<text class="stop-text">停止执行</text>
						</view>
						<!-- 执行工作流按钮（在非执行状态时显示） -->
						<view
							v-else
							class="execute-workflow-btn"
							:class="{ 'disabled': !canExecuteWorkflow || isExecutingWorkflow }"
							@click="executeWorkflow"
						>
							<text v-if="!isExecutingWorkflow">⚡ 执行工作流</text>
							<text v-else>执行中...</text>
						</view>


					</view>
				</view>
			</view>


		</view>
	</view>


		<!-- 自定义点数不足弹窗 -->
		<view v-if="showPointsModal" class="points-modal-overlay" @click="closePointsModal">
			<view class="points-modal" @click.stop>
				<!-- 弹窗头部 -->
				<view class="points-modal-header">
					<view class="points-icon">💎</view>
					<text class="points-title">点数不足</text>
					<view class="points-close-btn" @click="closePointsModal">
						<text class="points-close-icon">✕</text>
					</view>
				</view>

				<!-- 弹窗内容 -->
				<view class="points-modal-content">
					<view class="points-info-card">
						<view class="points-current">
							<text class="points-label">当前剩余</text>
							<text class="points-value">{{ remainingPoints || 0 }}</text>
							<text class="points-unit">点数</text>
						</view>
						<view class="points-divider"></view>
						<view class="points-required">
							<text class="points-label">执行需要</text>
							<text class="points-value required">{{ requiredPointsForModal || 0 }}</text>
							<text class="points-unit">点数</text>
						</view>
					</view>

					<view class="points-message">
						<text class="message-text">执行此工作流需要 {{ requiredPointsForModal || 0 }} 点数，您当前剩余 {{ remainingPoints || 0 }} 点数。</text>
						<text class="message-sub">请购买套餐获取更多点数继续使用。</text>
					</view>
				</view>

				<!-- 弹窗按钮 -->
				<view class="points-modal-actions">
					<view class="points-btn points-btn-cancel" @click="closePointsModal">
						<text class="points-btn-text">取消</text>
					</view>
					<view class="points-btn points-btn-primary" @click="handleBuyPackage">
						<text class="points-btn-text">购买套餐</text>
					</view>
				</view>
			</view>
		</view>

	<!-- 自定义高层级复制提示 -->
	<view v-if="showCopyToast" class="custom-toast-overlay">
		<view class="custom-toast" :class="{ 'success': copyToastType === 'success', 'error': copyToastType === 'error' }">
			<text class="toast-icon">{{ copyToastType === 'success' ? '✅' : '❌' }}</text>
			<text class="toast-text">{{ copyToastMessage }}</text>
		</view>
	</view>


</template>

<script>
import { cozeApi } from '@/api/coze-api.js'
import agentThemeManager from '@/api/agent-themes.js'
import { IMAGE_BASE_URL, API_BASE_URL } from '@/config/index.js'
import { userStore } from '@/api/members.js'
import workflowHistoryApi from '@/api/workflow-history.js'
import shareApi from '@/api/share.js'

export default {
	data() {
		return {
			// 工作流相关数据
			workflowParams: [], // 工作流参数配置
			workflowInputs: {}, // 工作流参数输入值
			workflowResult: '', // 工作流执行结果
			isExecutingWorkflow: false, // 是否正在执行工作流
			workflowExecutionId: null, // 工作流执行ID
			workflowRunId: null, // 工作流运行ID
			showWorkflowModal: false, // 是否显示工作流弹窗
			workflowConsumption: 1000, // 工作流执行消耗点数

			// 图片加载状态
			imageLoadingState: 'idle', // idle, loading, loaded, error

			// 历史记录相关
			showHistoryModal: false, // 是否显示历史记录弹窗
			historyList: [], // 历史记录列表
			historyLoading: false, // 历史记录加载状态
			historyPage: 1, // 历史记录页码
			hasMoreHistory: true, // 是否还有更多历史记录

			// 分享相关
			showShareModal: false, // 是否显示分享弹窗
			shareCode: '', // 分享邀请码
			shareConfig: null, // 分享配置

			// 自定义弹窗相关
			showPointsModal: false, // 是否显示点数不足弹窗
			requiredPointsForModal: 0, // 弹窗显示的所需点数

			// 智能体配置
			currentTheme: null, // 当前智能体主题
			botId: null, // 当前智能体ID
			appId: null, // 应用ID

			// 用户配置
			userId: 'user_' + Date.now(), // 用户ID
			conversationId: null, // 对话ID，用于维持对话上下文

			// UI状态
			showWelcome: true, // 是否显示欢迎消息
			errorMessage: '', // 错误消息
			isApiInitialized: false, // API是否已初始化

			// 点数相关
			remainingPoints: 0, // 当前剩余点数
			consumption: 1, // 当前每条消息扣除点数

			// 页面显示数据
			agentTitle: '智能体标题', // 智能体标题
			agentSubtitle: '消息提分：2', // 智能体副标题
			agentName: '智能体标题', // 智能体名称
			agentDescription: '智能体描述', // 智能体描述
			agentAvatar: '/static/images/znt_avatar.png', // 智能体头像
			welcomeTitle: 'AI智能体', // 欢迎标题
			welcomeDesc: '智能体描述', // 欢迎描述

			// 多媒体内容数据 - 三个固定类型
			mediaContents: {
				// 案例视频展示
				video: {
					enabled: true, // 是否启用
					title: '案例视频展示',
					content: 'https://example.com/demo_video.mp4',
					description: '观看实际操作演示视频'
				},
				// 案例图片展示
				image: {
					enabled: true, // 是否启用
					title: '案例图片展示',
					content: '/static/images/znt_avatar.png', // 暂时使用现有图片
					description: '查看详细图片案例说明'
				},
				// 文字教学
				text: {
					enabled: true, // 是否启用
					title: '文字教学',
					content: '这里是详细的文字教学内容，包含操作步骤、注意事项和相关说明。用户可以通过阅读这些内容来学习如何使用相关功能...',
					description: '阅读详细的操作指南'
				}
			},

			// 重试相关
			retryCount: 0, // 重试次数
			maxRetries: 3, // 最大重试次数

			// 网络状态
			isOnline: true, // 是否在线

			// 视频自适应高度
			videoHeight: 250, // 默认高度

			// API引用
			cozeApi: cozeApi,

			// 用户相关
			isLoggedIn: false, // 是否已登录
			userInfo: null, // 用户信息

			// 复制状态跟踪
			copiedLinks: new Set(), // 用于跟踪已复制的链接

			// 自定义Toast提示
			showCopyToast: false, // 是否显示复制提示
			copyToastMessage: '', // 提示消息
			copyToastType: 'success' // 提示类型：success 或 error

			// 魔法导航栏相关
			// navItems: [
			// 	{ text: '初页', icon: 'icon-home', path: '/pages/index/index' },
			// 	{ text: '同创', icon: 'icon-robot', path: '/pages/index/znt' },
			// 	{ text: '会享', icon: 'icon-message', path: '/pages/chat/index' },
			// 	{ text: '往档', icon: 'icon-history', path: '/pages/history/index' },
			// 	{ text: '吾界', icon: 'icon-user', path: '/pages/profile/index' }
			// ]
		}
	},

	async onLoad(options) {
		console.log('=== znt.vue onLoad 开始 ===')
		console.log('接收到的参数:', options)
		console.log('参数类型:', typeof options)
		console.log('参数键值:', options ? Object.keys(options) : 'null')

		// 检查用户登录状态
		this.checkUserLoginStatus()

		// 处理邀请码参数
		if (options && options.inviter) {
			console.log('检测到邀请人ID:', options.inviter)
			// 如果用户已登录，建立邀请关系
			if (userStore.isLoggedIn()) {
				await this.handleInviterRelation(options.inviter)
			} else {
				// 保存邀请人信息，等用户登录后处理
				uni.setStorageSync('pendingInviter', options.inviter)
			}
		}

		// 处理邀请码参数
		if (options && options.inviteCode) {
			console.log('检测到邀请码:', options.inviteCode)
			await this.handleInviteCodeAccess(options.inviteCode)
		}

		// 获取应用ID（首页传递的参数名是id）
		if (options && options.id) {
			this.appId = options.id
			console.log('✅ 设置应用ID:', this.appId)
		} else {
			console.warn('⚠️ 未接收到应用ID参数，将使用默认配置')
			console.log('当前URL参数:', window.location ? window.location.search : 'N/A')
		}

		// 初始化默认图片URL
		this.initializeImageUrls()

		// 监听网络状态
		this.setupNetworkListener()
		await this.initializeChat()
	},

	onUnload() {
		// 清理网络监听
		uni.offNetworkStatusChange()
	},

	// 微信小程序分享配置
	// #ifdef MP-WEIXIN
	onShareAppMessage() {
		const currentUser = userStore.getCurrentUser()
		const shareConfig = this.shareConfig || {}

		return {
			title: shareConfig.share_title || 'AI智能体使用邀请',
			desc: shareConfig.share_description || '注册即可获得额外免费使用次数，快来体验智能AI助手！',
			imageUrl: shareConfig.share_image_url || '',
			path: `/pages/index/znt?id=${this.appId}&inviter=${currentUser?.id || ''}`
		}
	},

	onShareTimeline() {
		const currentUser = userStore.getCurrentUser()
		const shareConfig = this.shareConfig || {}

		return {
			title: shareConfig.share_title || 'AI智能体使用邀请',
			imageUrl: shareConfig.share_image_url || '',
			query: `id=${this.appId}&inviter=${currentUser?.id || ''}`
		}
	},
	// #endif

	computed: {
		// 智能体信息
		agentInfo() {
			return {
				name: this.currentTheme?.name || '智能助手',
				description: this.currentTheme?.description || '您的AI智能助手',
				avatar: this.currentTheme?.avatar || '/static/default-avatar.png'
			}
		},

		// 是否可以执行工作流
		canExecuteWorkflow() {
			console.log('🔍 检查工作流执行条件:')
			console.log('- workflowParams:', this.workflowParams)
			console.log('- workflowInputs:', this.workflowInputs)
			console.log('- isExecutingWorkflow:', this.isExecutingWorkflow)

			// 如果没有参数配置，不能执行
			if (!this.workflowParams || this.workflowParams.length === 0) {
				console.log('❌ 没有工作流参数配置')
				return false
			}

			// 检查必填参数是否都已填写
			const requiredParams = this.workflowParams.filter(param => param.required)
			console.log('🔍 必填参数列表:', requiredParams.map(p => p.name))

			const requiredValid = requiredParams.every(param => {
				const value = this.workflowInputs[param.name]
				const isValid = value !== undefined && value !== null && value.toString().trim() !== ''
				console.log(`🔍 参数 ${param.name}: 值="${value}", 有效=${isValid}`)
				return isValid
			})

			// 如果有必填参数但未填写，不能执行
			if (requiredParams.length > 0 && !requiredValid) {
				console.log('❌ 必填参数未填写完整')
				return false
			}

			// 如果没有必填参数，检查是否至少有一个参数有值
			if (requiredParams.length === 0) {
				const hasAnyValue = this.workflowParams.some(param => {
					const value = this.workflowInputs[param.name]
					const hasValue = value !== undefined && value !== null && value.toString().trim() !== ''
					console.log(`🔍 检查参数 ${param.name}: 值="${value}", 有值=${hasValue}`)
					return hasValue
				})
				console.log('✅ 至少有一个参数有值:', hasAnyValue)
				return hasAnyValue
			}

			console.log('✅ 所有必填参数已填写')
			return true
		}
	},

	methods: {

		// ========== 工作流相关方法 ==========

		// 打开工作流弹窗
		async openWorkflowModal() {
			console.log('尝试打开工作流弹窗')

			// 获取用户点数信息
			await this.fetchUserPoints()

			// 🔍 获取当前主题的消耗点数（优先使用agent_theme表中的consumption字段）
			console.log('🔍 详细的主题信息调试:', {
				hasCurrentTheme: !!this.currentTheme,
				currentThemeKeys: this.currentTheme ? Object.keys(this.currentTheme) : [],
				themeConsumption: this.currentTheme?.consumption,
				themeConsumptionType: typeof this.currentTheme?.consumption,
				workflowConsumption: this.workflowConsumption,
				workflowConsumptionType: typeof this.workflowConsumption,
				appId: this.appId,
				fullTheme: this.currentTheme
			})

			// 🚨 强制重新获取主题配置，确保数据是最新的
			if (this.appId) {
				console.log('🔄 强制重新获取主题配置，ID:', this.appId)
				try {
					const freshTheme = await agentThemeManager.apiClient.getAgentThemeById(this.appId)
					console.log('🆕 重新获取的主题配置:', freshTheme)
					console.log('🆕 重新获取的consumption:', freshTheme?.consumption)
					this.currentTheme = freshTheme
				} catch (error) {
					console.error('❌ 重新获取主题配置失败:', error)
				}
			}

			// 🔍 强制使用数据库中的consumption字段，确保优先级正确
			let requiredPoints = 1000; // 默认值

			if (this.currentTheme && typeof this.currentTheme.consumption === 'number' && this.currentTheme.consumption >= 0) {
				requiredPoints = this.currentTheme.consumption;
				console.log('✅ 使用主题数据库consumption字段:', requiredPoints);
			} else if (this.workflowConsumption && this.workflowConsumption !== 1000) {
				requiredPoints = this.workflowConsumption;
				console.log('⚠️ 使用workflowConsumption备用值:', requiredPoints);
			} else {
				console.log('❌ 使用默认值1000，原因：');
				console.log('  - currentTheme存在:', !!this.currentTheme);
				console.log('  - consumption值:', this.currentTheme?.consumption);
				console.log('  - consumption类型:', typeof this.currentTheme?.consumption);
				console.log('  - workflowConsumption:', this.workflowConsumption);
			}

			console.log('💰 打开弹窗时的点数检查:', {
				userPoints: this.remainingPoints,
				requiredPoints: requiredPoints,
				themeConsumption: this.currentTheme?.consumption,
				fallbackConsumption: this.workflowConsumption,
				finalCalculatedPoints: requiredPoints
			})

			// 检查用户点数是否足够
			if (this.remainingPoints < requiredPoints) {
				console.log('❌ 用户点数不足，无法打开工作流弹窗')
				// 使用自定义弹窗替代原生弹窗
				this.showPointsInsufficientModal(requiredPoints)
				return
			}

			// 初始化工作流参数
			this.initWorkflowParams()

			this.showWorkflowModal = true
			console.log('打开工作流弹窗')

			// 延迟强制更新，确保输入框正常渲染
			this.$nextTick(() => {
				console.log('弹窗渲染完成，强制更新输入框')
				this.$forceUpdate()
			})
		},

		// 关闭工作流弹窗
		closeWorkflowModal() {
			this.showWorkflowModal = false
			this.setWorkflowResult('')
			this.workflowInputs = {}
			console.log('关闭工作流弹窗')
		},

		// 处理弹窗遮罩层点击事件
		onModalOverlayClick(event) {
			// 只有当点击的是遮罩层本身时才关闭弹窗
			if (event.target === event.currentTarget) {
				this.closeWorkflowModal()
			}
		},

		// 调试输入框点击事件
		onInputClick(paramName) {
			console.log('输入框被点击:', paramName)
			console.log('当前输入值:', this.workflowInputs[paramName])
			console.log('工作流参数:', this.workflowParams)
			console.log('所有输入值:', this.workflowInputs)
		},

		// 处理输入框内容变化
		onInputChange(paramName, event) {
			const value = event.target.value
			console.log('输入框内容变化:', paramName, value)

			// 使用$set确保响应式更新
			this.$set(this.workflowInputs, paramName, value)

			console.log('更新后的值:', this.workflowInputs[paramName])
			console.log('完整workflowInputs:', this.workflowInputs)
		},

		// 处理可编辑div的输入
		onDivInput(paramName, event) {
			const value = event.target.textContent || event.target.innerText || ''
			console.log('可编辑div内容变化:', paramName, value)

			// 使用$set确保响应式更新
			this.$set(this.workflowInputs, paramName, value)

			console.log('更新后的值:', this.workflowInputs[paramName])
		},

		// 🔧 处理工作流输入变化 - 简化版本
		onWorkflowInputChange(paramName, event) {
			// 兼容不同的事件格式（uni-app vs 标准web）
			let value = ''
			if (event && event.target) {
				value = event.target.value
			} else if (event && event.detail) {
				value = event.detail.value
			} else if (typeof event === 'string') {
				value = event
			}

			console.log(`📝 工作流参数输入 [${paramName}]:`, value)
			console.log('🔍 输入前的workflowInputs:', this.workflowInputs)

			// 🔧 直接赋值，因为v-model已经处理了响应式
			// this.$set(this.workflowInputs, paramName, value)

			console.log('✅ 更新后的工作流输入:', this.workflowInputs)
			console.log('✅ 参数验证状态:', this.canExecuteWorkflow)
		},

		// 测试输入框焦点
		testInputFocus(paramName) {
			console.log('测试输入框焦点:', paramName)
			console.log('当前workflowInputs:', this.workflowInputs)

			// 使用$set设置测试值，确保响应式
			const testValue = '测试输入内容_' + Date.now()
			this.$set(this.workflowInputs, paramName, testValue)

			console.log('设置测试值后:', this.workflowInputs[paramName])
			console.log('完整workflowInputs:', this.workflowInputs)

			uni.showToast({
				title: `输入框 ${paramName} 测试成功`,
				icon: 'success'
			})
		},

		// 初始化工作流参数
		initWorkflowParams() {
			console.log('🔄 初始化工作流参数')
			console.log('🔍 当前主题:', this.currentTheme)

			// 🔍 详细调试主题数据结构
			console.log('🔍 主题数据详细调试:')
			console.log('- 主题是否存在:', !!this.currentTheme)
			console.log('- 主题类型:', typeof this.currentTheme)
			console.log('- 主题所有字段:', this.currentTheme ? Object.keys(this.currentTheme) : [])
			console.log('- 完整主题对象:', JSON.stringify(this.currentTheme, null, 2))

			// 🔍 尝试多种方式访问parameters字段
			console.log('🔍 多种方式访问parameters:')
			console.log('- this.currentTheme.parameters:', this.currentTheme?.parameters)
			console.log('- this.currentTheme["parameters"]:', this.currentTheme?.["parameters"])
			console.log('- 直接遍历查找parameters:')
			if (this.currentTheme) {
				for (const [key, value] of Object.entries(this.currentTheme)) {
					if (key.toLowerCase().includes('param')) {
						console.log(`  - 找到参数相关字段 ${key}:`, value)
					}
				}
			}

			// 优先从parameters字段获取扣子官方格式参数，兼容旧版本params字段
			let paramConfig = null

			// 🔍 更严格的参数字段检查
			console.log('🔍 检查参数字段:')
			console.log('- parameters存在且为数组:', this.currentTheme?.parameters && Array.isArray(this.currentTheme.parameters))
			console.log('- parameters长度:', this.currentTheme?.parameters?.length)
			console.log('- params存在且为数组:', this.currentTheme?.params && Array.isArray(this.currentTheme.params))
			console.log('- formFields存在且为数组:', this.currentTheme?.formFields && Array.isArray(this.currentTheme.formFields))

			if (this.currentTheme?.parameters && Array.isArray(this.currentTheme.parameters) && this.currentTheme.parameters.length > 0) {
				paramConfig = this.currentTheme.parameters
				console.log('✅ 使用扣子官方parameters字段:', paramConfig)
			} else if (this.currentTheme?.params && Array.isArray(this.currentTheme.params) && this.currentTheme.params.length > 0) {
				paramConfig = this.currentTheme.params
				console.log('⚠️ 使用兼容params字段:', paramConfig)
			} else if (this.currentTheme?.formFields && Array.isArray(this.currentTheme.formFields) && this.currentTheme.formFields.length > 0) {
				paramConfig = this.currentTheme.formFields
				console.log('⚠️ 使用兼容formFields字段:', paramConfig)
			} else {
				console.log('❌ 未找到有效的参数配置')
				console.log('- parameters:', this.currentTheme?.parameters)
				console.log('- params:', this.currentTheme?.params)
				console.log('- formFields:', this.currentTheme?.formFields)
			}

			if (paramConfig && Array.isArray(paramConfig)) {
				this.workflowParams = paramConfig
				console.log('✅ 工作流参数配置:', this.workflowParams)

				// 🔍 详细检查每个参数的结构
				this.workflowParams.forEach((param, index) => {
					console.log(`参数${index + 1}:`, {
						name: param.name,
						label: param.label,
						type: param.type,
						required: param.required,
						default: param.default,
						defaultValue: param.defaultValue,
						完整对象: param
					})
				})

				// 🔧 重新创建workflowInputs对象，确保响应式
				const newInputs = {}
				this.workflowParams.forEach(param => {
					// 根据参数类型设置默认值
					let defaultValue = param.default || param.defaultValue || ''

					// 多选类型默认为数组
					if (param.type === 'checkbox') {
						defaultValue = Array.isArray(defaultValue) ? defaultValue : []
					}

					newInputs[param.name] = defaultValue
					console.log(`设置参数 ${param.name} = "${defaultValue}"`)
				})

				// 🔧 直接赋值而不是使用$set
				this.workflowInputs = { ...newInputs }

				// 🔧 强制更新视图
				this.$forceUpdate()

				console.log('✅ 初始化参数值:', this.workflowInputs)
				console.log('✅ workflowInputs类型:', typeof this.workflowInputs)
			} else {
				// 如果没有配置参数，使用默认参数
				console.log('❌ 未找到参数配置，使用默认参数')
				console.log('原始parameters字段:', this.currentTheme?.parameters)

				// 🔧 使用适合logo设计的默认参数配置
				this.workflowParams = [
					{
						name: 'input',
						label: 'logo设计需求',
						type: 'text',
						required: true,
						description: '请输入logo设计需求',
						placeholder: '请详细描述您的logo设计需求，包括风格、颜色、行业等...'
					}
				]

				console.log('✅ 默认参数配置已应用:', this.workflowParams)

				this.$set(this, 'workflowInputs', { input: '' })
				this.$set(this.workflowInputs, 'input', '')
			}
		},

		// ========== 参数输入处理方法 ==========



		// 获取选择器当前索引
		getSelectIndex(param) {
			if (!param.options || !this.workflowInputs[param.name]) {
				return 0
			}
			const index = param.options.indexOf(this.workflowInputs[param.name])
			return index >= 0 ? index : 0
		},

		// 下拉选择器变化事件
		onParamSelectChange(paramName, event) {
			const selectedIndex = event.detail.value
			const param = this.workflowParams.find(p => p.name === paramName)
			if (param && param.options) {
				this.$set(this.workflowInputs, paramName, param.options[selectedIndex])
				console.log(`选择器 ${paramName} 值变更为:`, this.workflowInputs[paramName])
			}
		},

		// 气泡按钮点击事件
		onBubbleButtonClick(paramName, option) {
			console.log('气泡按钮点击:', paramName, option)
			this.$set(this.workflowInputs, paramName, option)
			console.log(`气泡按钮 ${paramName} 值变更为:`, this.workflowInputs[paramName])
		},

		// 多选按钮点击事件
		onCheckboxButtonClick(paramName, option) {
			let currentValues = this.workflowInputs[paramName] || []
			if (!Array.isArray(currentValues)) {
				currentValues = []
			}

			const index = currentValues.indexOf(option)
			if (index > -1) {
				// 如果已选中，则取消选中
				currentValues.splice(index, 1)
			} else {
				// 如果未选中，则添加选中
				currentValues.push(option)
			}

			this.$set(this.workflowInputs, paramName, currentValues)
			console.log(`多选 ${paramName} 值变更为:`, this.workflowInputs[paramName])
		},

		// 检查复选框是否选中
		isCheckboxChecked(paramName, option) {
			const values = this.workflowInputs[paramName]
			return Array.isArray(values) && values.includes(option)
		},

		// 上传图片
		async uploadImage(paramName) {
			console.log('上传图片:', paramName)

			try {
				const result = await new Promise((resolve, reject) => {
					uni.chooseImage({
						count: 1,
						sizeType: ['compressed'],
						sourceType: ['album', 'camera'],
						success: resolve,
						fail: reject
					})
				})

				if (result.tempFilePaths && result.tempFilePaths.length > 0) {
					const tempFilePath = result.tempFilePaths[0]
					console.log('选择的图片路径:', tempFilePath)

					// 显示上传进度
					uni.showLoading({
						title: '上传中...'
					})

					// 调用上传接口
					const uploadResult = await this.uploadFile(tempFilePath, 'image')

					if (uploadResult && uploadResult.url) {
						this.$set(this.workflowInputs, paramName, uploadResult.url)
						console.log(`图片上传成功，${paramName} 设置为:`, uploadResult.url)

						uni.showToast({
							title: '上传成功',
							icon: 'success'
						})
					} else {
						throw new Error('上传失败，未获取到文件URL')
					}
				}
			} catch (error) {
				console.error('上传图片失败:', error)
				uni.showToast({
					title: '上传失败',
					icon: 'error'
				})
			} finally {
				uni.hideLoading()
			}
		},

		// 上传视频
		async uploadVideo(paramName) {
			console.log('上传视频:', paramName)

			try {
				const result = await new Promise((resolve, reject) => {
					uni.chooseVideo({
						sourceType: ['album', 'camera'],
						maxDuration: 60,
						camera: 'back',
						success: resolve,
						fail: reject
					})
				})

				if (result.tempFilePath) {
					console.log('选择的视频路径:', result.tempFilePath)

					// 显示上传进度
					uni.showLoading({
						title: '上传中...'
					})

					// 调用上传接口
					const uploadResult = await this.uploadFile(result.tempFilePath, 'video')

					if (uploadResult && uploadResult.url) {
						this.$set(this.workflowInputs, paramName, uploadResult.url)
						console.log(`视频上传成功，${paramName} 设置为:`, uploadResult.url)

						uni.showToast({
							title: '上传成功',
							icon: 'success'
						})
					} else {
						throw new Error('上传失败，未获取到文件URL')
					}
				}
			} catch (error) {
				console.error('上传视频失败:', error)
				uni.showToast({
					title: '上传失败',
					icon: 'error'
				})
			} finally {
				uni.hideLoading()
			}
		},

		// 通用文件上传方法
		async uploadFile(filePath, fileType) {
			return new Promise((resolve, reject) => {
				console.log('开始上传文件:', { filePath, fileType })

				// 首先测试网络连接
				uni.request({
					url: `${API_BASE_URL}/api/media`,
					method: 'GET',
					success: (testRes) => {
						console.log('网络连接测试成功:', testRes)
					},
					fail: (testErr) => {
						console.error('网络连接测试失败:', testErr)
					}
				})

				// 获取用户token
				const token = uni.getStorageSync('token')

				console.log('上传参数:', {
					url: `${API_BASE_URL}/api/media/upload`,
					filePath: filePath,
					name: 'file',
					formData: {
						type: fileType,
						groupId: 'workflow'
					},
					token: token ? 'Bearer ' + token.substring(0, 20) + '...' : '无token'
				})

				const uploadTask = uni.uploadFile({
					url: `${API_BASE_URL}/api/media/upload`,
					filePath: filePath,
					name: 'file',
					formData: {
						type: fileType,
						groupId: 'workflow' // 工作流文件分组
					},
					header: {
						'Authorization': token ? `Bearer ${token}` : ''
						// 注意：不要手动设置 Content-Type，让uni.uploadFile自动处理
					},
					success: (res) => {
						console.log('上传响应完整信息:', {
							statusCode: res.statusCode,
							data: res.data,
							header: res.header
						})

						try {
							// 检查HTTP状态码
							if (res.statusCode !== 200 && res.statusCode !== 201) {
								console.error('HTTP状态码错误:', res.statusCode, '响应数据:', res.data)
								reject(new Error(`HTTP错误: ${res.statusCode} - ${res.data || '未知错误'}`))
								return
							}

							// 尝试解析JSON响应
							let data
							try {
								data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data
							} catch (parseError) {
								console.error('JSON解析失败:', parseError, '原始数据:', res.data)
								reject(new Error('服务器响应格式错误'))
								return
							}

							console.log('解析后的数据:', data)

							// 根据不同的响应格式处理
							if (res.statusCode === 201 && data.url) {
								// 新版本API格式 (status 201, 直接返回媒体对象)
								// 确保URL是完整的可访问路径
								const fullUrl = data.url.startsWith('http') ? data.url : `${IMAGE_BASE_URL}${data.url}`
								resolve({
									url: fullUrl,
									id: data.id,
									name: data.name
								})
							} else if (data.code === 200 && data.data) {
								// 旧版本API格式 (code 200, data包装)
								if (data.data.url) {
									const fullUrl = data.data.url.startsWith('http') ? data.data.url : `${IMAGE_BASE_URL}${data.data.url}`
									data.data.url = fullUrl
								}
								resolve(data.data)
							} else if (data.url) {
								// 直接包含url的格式
								const fullUrl = data.url.startsWith('http') ? data.url : `${IMAGE_BASE_URL}${data.url}`
								resolve({
									...data,
									url: fullUrl
								})
							} else {
								console.error('上传响应格式不正确:', data)
								reject(new Error(data.message || data.error || '上传失败，未获取到文件URL'))
							}
						} catch (error) {
							console.error('处理上传响应失败:', error, '原始响应:', res)
							reject(new Error('处理上传响应失败: ' + error.message))
						}
					},
					fail: (error) => {
						console.error('上传请求失败完整信息:', {
							errMsg: error.errMsg,
							errCode: error.errCode,
							error: error
						})

						// 根据错误类型提供更具体的错误信息
						let errorMessage = '上传失败'
						if (error.errMsg) {
							if (error.errMsg.includes('timeout')) {
								errorMessage = '上传超时，请检查网络连接'
							} else if (error.errMsg.includes('fail')) {
								errorMessage = '网络连接失败，请检查服务器状态'
							} else {
								errorMessage = error.errMsg
							}
						}

						reject(new Error(errorMessage))
					}
				})

				// 监听上传进度
				uploadTask.onProgressUpdate((res) => {
					console.log('上传进度:', res.progress + '%')
					console.log('已上传:', res.totalBytesSent)
					console.log('总大小:', res.totalBytesExpectedToSend)
				})
			})
		},

		// 预览上传的图片
		previewUploadedImage(imageUrl) {
			uni.previewImage({
				urls: [imageUrl],
				current: imageUrl
			})
		},

		// 移除上传的文件
		removeUploadedFile(paramName) {
			this.$set(this.workflowInputs, paramName, '')
			console.log(`移除文件，${paramName} 重置为空`)
		},



		// 执行工作流
		async executeWorkflow() {
			console.log('=== 开始执行工作流 ===')
			console.log('工作流ID:', this.currentTheme?.workflowId)
			console.log('输入参数:', this.workflowInputs)
			console.log('🔍 详细调试信息:')
			console.log('  - workflowParams:', this.workflowParams)
			console.log('  - workflowInputs类型:', typeof this.workflowInputs)
			console.log('  - workflowInputs是否为空对象:', Object.keys(this.workflowInputs).length === 0)
			console.log('  - currentTheme.parameters:', this.currentTheme?.parameters)

			// 🔧 验证必填参数
			console.log('🔍 验证必填参数...')
			console.log('当前参数配置:', this.workflowParams)
			console.log('当前输入值:', this.workflowInputs)

			const requiredParams = this.workflowParams.filter(param => param.required)
			const missingParams = []

			for (const param of requiredParams) {
				const value = this.workflowInputs[param.name]
				// 对于数组类型（多选），检查是否有值
				if (Array.isArray(value)) {
					if (value.length === 0) {
						missingParams.push(param.label || param.name)
					}
				} else {
					// 对于字符串类型，检查是否非空
					if (!value || value.toString().trim() === '') {
						missingParams.push(param.label || param.name)
					}
				}
			}

			if (missingParams.length > 0) {
				console.log('❌ 缺少必填参数:', missingParams)
				uni.showToast({
					title: `请填写: ${missingParams.join(', ')}`,
					icon: 'none',
					duration: 3000
				})
				return
			}

			console.log('✅ 所有必填参数验证通过')

			if (!this.canExecuteWorkflow || this.isExecutingWorkflow) {
				console.log('无法执行工作流：参数不完整或正在执行中')
				return
			}

			if (!this.currentTheme?.workflowId) {
				uni.showToast({
					title: '工作流未配置',
					icon: 'error'
				})
				return
			}

			// 🔍 获取最新的用户点数和主题消耗点数
			await this.fetchUserPoints()

			// 🔍 详细调试消耗点数的计算过程
			console.log('🔍 执行工作流时的详细调试:', {
				hasCurrentTheme: !!this.currentTheme,
				currentThemeId: this.currentTheme?.id,
				currentThemeTitle: this.currentTheme?.title,
				currentThemeConsumption: this.currentTheme?.consumption,
				currentThemeConsumptionType: typeof this.currentTheme?.consumption,
				workflowConsumption: this.workflowConsumption,
				workflowConsumptionType: typeof this.workflowConsumption,
				appId: this.appId,
				fullCurrentTheme: JSON.stringify(this.currentTheme, null, 2)
			})

			// 🔍 强制使用数据库中的consumption字段，确保优先级正确
			let requiredPoints = 1000; // 默认值

			if (this.currentTheme && typeof this.currentTheme.consumption === 'number' && this.currentTheme.consumption >= 0) {
				requiredPoints = this.currentTheme.consumption;
				console.log('✅ 执行工作流时使用主题consumption字段:', requiredPoints);
			} else if (this.workflowConsumption && this.workflowConsumption !== 1000) {
				requiredPoints = this.workflowConsumption;
				console.log('⚠️ 执行工作流时使用workflowConsumption备用值:', requiredPoints);
			} else {
				console.log('❌ 执行工作流时使用默认值1000，原因：');
				console.log('  - currentTheme存在:', !!this.currentTheme);
				console.log('  - consumption值:', this.currentTheme?.consumption);
				console.log('  - consumption类型:', typeof this.currentTheme?.consumption);
				console.log('  - workflowConsumption:', this.workflowConsumption);
			}

			console.log('💰 点数检查:', {
				userPoints: this.remainingPoints,
				requiredPoints: requiredPoints,
				sufficient: this.remainingPoints >= requiredPoints,
				themeConsumption: this.currentTheme?.consumption,
				fallbackConsumption: this.workflowConsumption,
				finalCalculatedPoints: requiredPoints
			})

			// 检查用户点数是否足够
			if (this.remainingPoints < requiredPoints) {
				console.log('❌ 用户点数不足，无法执行工作流')

				// 使用自定义弹窗替代原生弹窗
				this.showPointsInsufficientModal(requiredPoints)
				return
			}

			try {
				this.isExecutingWorkflow = true
				this.setWorkflowResult('')

				console.log('🚀 调用工作流执行API')
				console.log('📊 传递给API的参数:', {
					workflowId: this.currentTheme.workflowId,
					parameters: this.workflowInputs,
					options: {
						userId: this.userId,
						stream: false,
						consumption: requiredPoints
					}
				})

				// 🔍 详细调试扣子API格式
				const cozeApiFormat = {
					workflow_id: this.currentTheme.workflowId,
					parameters: this.workflowInputs,
					user_id: this.userId || 'default_user',
					stream: false
				}
				console.log('📋 扣子官方API格式:', JSON.stringify(cozeApiFormat, null, 2))

				// 调用coze-api执行工作流
				const response = await cozeApi.runWorkflow(
					this.currentTheme.workflowId,
					this.workflowInputs,
					{
						userId: this.userId,
						stream: false,
						consumption: requiredPoints // 传递正确的消耗点数
					}
				)

				console.log('工作流执行响应:', response)

				// 处理响应结果
				if (response && response.code === 0) {
					// 成功响应
					let data = response.data

					// 立即扣除点数显示
					console.log('💰 工作流执行成功，立即扣除点数显示')
					this.remainingPoints = Math.max(0, this.remainingPoints - requiredPoints)

					// 🔍 处理data字段可能是字符串的情况
					if (typeof data === 'string') {
						try {
							data = JSON.parse(data)
							console.log('📝 解析字符串格式的data:', data)
						} catch (parseError) {
							console.warn('⚠️ 无法解析data字符串，使用原始值:', data)
							// 如果无法解析，直接使用字符串作为结果
							this.setWorkflowResult(data)
							console.log('✅ 工作流执行完成，结果:', this.workflowResult)
							return
						}
					}

					if (data && data.execute_id) {
						// 异步执行，需要轮询结果
						this.workflowExecutionId = data.execute_id
						console.log('异步执行，开始轮询结果:', data.execute_id)
						await this.pollWorkflowResult(data.execute_id)
					} else if (data && (data.result || data.output)) {
						// 同步执行，直接获取结果
						this.setWorkflowResult(data.result || data.output || '执行完成')
						console.log('同步执行完成，结果:', this.workflowResult)
					} else if (data) {
						// 如果data是对象但没有特定字段，尝试提取有用信息
						console.log('📊 处理复杂data对象:', data)

						// 查找可能的输出字段
						const possibleOutputs = Object.keys(data).filter(key =>
							key.includes('output') || key.includes('result') || key.includes('url')
						)

						if (possibleOutputs.length > 0) {
							const outputKey = possibleOutputs[0]
							this.setWorkflowResult(data[outputKey])
							console.log(`✅ 找到输出字段 ${outputKey}:`, this.workflowResult)
						} else {
							this.setWorkflowResult(JSON.stringify(data, null, 2))
							console.log('✅ 使用完整data作为结果:', this.workflowResult)
						}
					} else {
						this.setWorkflowResult('工作流执行完成')
						console.log('✅ 工作流执行完成，无具体结果')
					}
				} else {
					// 错误响应
					const errorMsg = response?.message || response?.msg || '工作流执行失败'
					throw new Error(errorMsg)
				}

			} catch (error) {
				console.error('工作流执行失败:', error)

				// 如果是点数不足的错误，特殊处理
				if (error.message && error.message.includes('点数不足')) {
					uni.showModal({
						title: '点数不足',
						content: '您的点数不足，请购买套餐获取更多点数。',
						confirmText: '购买套餐',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								this.navigateToMembershipPage()
							}
						}
					})
				} else {
					this.setWorkflowResult(`执行失败: ${error.message}`)
					uni.showToast({
						title: '执行失败',
						icon: 'error'
					})
				}
			} finally {
				this.isExecutingWorkflow = false
				this.workflowExecutionId = null
			}
		},

		// 轮询工作流执行结果
		async pollWorkflowResult(executeId, maxAttempts = 30) {
			console.log('开始轮询工作流结果:', executeId)

			for (let attempt = 1; attempt <= maxAttempts; attempt++) {
				try {
					console.log(`第${attempt}次轮询...`)

					const result = await cozeApi.getWorkflowExecutionResult(executeId)
					console.log('轮询结果:', result)

					if (result && result.code === 0 && result.data) {
						const status = result.data.status

						if (status === 'success' || status === 'completed') {
							// 执行成功
							this.setWorkflowResult(result.data.result || result.data.output || '执行完成')
							console.log('工作流执行成功，结果:', this.workflowResult)
							return
						} else if (status === 'failed' || status === 'error') {
							// 执行失败
							throw new Error(result.data.error || '工作流执行失败')
						}
						// 其他状态继续轮询
					}

					// 等待2秒后继续轮询
					await new Promise(resolve => setTimeout(resolve, 2000))

				} catch (error) {
					console.error(`第${attempt}次轮询失败:`, error)
					if (attempt === maxAttempts) {
						throw error
					}
				}
			}

			throw new Error('工作流执行超时')
		},

		// 停止工作流执行
		async stopWorkflowExecution() {
			console.log('用户请求停止工作流执行')

			this.isExecutingWorkflow = false
			this.workflowExecutionId = null
			this.setWorkflowResult('执行已停止')

			uni.showToast({
				title: '已停止执行',
				icon: 'success'
			})
		},

		// 判断是否为图片URL
		isImageUrl(url) {
			if (!url || typeof url !== 'string') return false

			// 常见图片文件扩展名
			const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico', '.tiff', '.tif']
			const lowerUrl = url.toLowerCase()

			// 检查文件扩展名
			const hasImageExtension = imageExtensions.some(ext => {
				// 检查URL是否以扩展名结尾，或者扩展名后面跟着查询参数
				return lowerUrl.includes(ext + '?') || lowerUrl.endsWith(ext)
			})

			// 检查常见图片服务域名
			const imageServices = [
				's.coze.cn',           // Coze图片服务
				'cdn.',                // CDN服务
				'img.',                // 图片服务
				'image.',              // 图片服务
				'pic.',                // 图片服务
				'photo.',              // 照片服务
				'imgur.com',           // Imgur
				'i.imgur.com',         // Imgur直链
				'github.com',          // GitHub (可能包含图片)
				'githubusercontent.com', // GitHub用户内容
				'qpic.cn',             // 腾讯图片
				'sinaimg.cn',          // 新浪图片
				'alicdn.com',          // 阿里云CDN
				'oss-cn-',             // 阿里云OSS
				'cos.ap-',             // 腾讯云COS
				'myqcloud.com',        // 腾讯云
				'amazonaws.com',       // AWS S3
				'cloudfront.net',      // AWS CloudFront
				'fastly.com',          // Fastly CDN
				'jsdelivr.net',        // jsDelivr CDN
				'unsplash.com',        // Unsplash
				'pexels.com',          // Pexels
				'pixabay.com'          // Pixabay
			]

			const hasImageService = imageServices.some(service => lowerUrl.includes(service))

			// 检查URL路径中是否包含图片相关关键词
			const hasImageKeyword = lowerUrl.includes('/image/') ||
									lowerUrl.includes('/img/') ||
									lowerUrl.includes('/pic/') ||
									lowerUrl.includes('/photo/') ||
									lowerUrl.includes('image') ||
									lowerUrl.includes('img')

			// 如果是HTTP/HTTPS链接且满足任一条件，则认为是图片
			const isHttpUrl = url.startsWith('http://') || url.startsWith('https://')

			return isHttpUrl && (hasImageExtension || hasImageService || hasImageKeyword)
		},

		// 判断是否为视频URL
		isVideoUrl(url) {
			if (!url || typeof url !== 'string') return false
			const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv']
			const lowerUrl = url.toLowerCase()
			return videoExtensions.some(ext => lowerUrl.includes(ext)) ||
				   lowerUrl.includes('video') ||
				   lowerUrl.includes('vid')
		},

		// 判断是否为URL
		isUrl(url) {
			if (!url || typeof url !== 'string') return false
			return url.startsWith('http://') || url.startsWith('https://') || url.startsWith('ftp://')
		},

		// 保存媒体文件
		async saveMedia(url, type) {
			try {
				console.log(`开始保存${type}:`, url)

				// 下载文件
				const downloadResult = await new Promise((resolve, reject) => {
					uni.downloadFile({
						url: url,
						success: resolve,
						fail: reject
					})
				})

				if (downloadResult.statusCode === 200) {
					// 保存到相册
					if (type === 'image') {
						await new Promise((resolve, reject) => {
							uni.saveImageToPhotosAlbum({
								filePath: downloadResult.tempFilePath,
								success: resolve,
								fail: reject
							})
						})
						uni.showToast({
							title: '图片已保存到相册',
							icon: 'success'
						})
					} else if (type === 'video') {
						await new Promise((resolve, reject) => {
							uni.saveVideoToPhotosAlbum({
								filePath: downloadResult.tempFilePath,
								success: resolve,
								fail: reject
							})
						})
						uni.showToast({
							title: '视频已保存到相册',
							icon: 'success'
						})
					}
				} else {
					throw new Error('下载失败')
				}
			} catch (error) {
				console.error(`保存${type}失败:`, error)
				uni.showToast({
					title: `保存${type}失败`,
					icon: 'error'
				})
			}
		},

		// 复制链接
		copyLink(url) {
			// 添加到已复制集合中
			this.copiedLinks.add(url)

			uni.setClipboardData({
				data: url,
				success: () => {
					// 显示自定义高层级提示
					this.showCustomToast('链接已复制', 'success', 2000)

					// 2秒后从已复制集合中移除
					setTimeout(() => {
						this.copiedLinks.delete(url)
					}, 2000)
				},
				fail: () => {
					// 复制失败时也要从集合中移除
					this.copiedLinks.delete(url)
					this.showCustomToast('复制失败', 'error', 2000)
				}
			})
		},

		// 复制文本
		copyText(text) {
			uni.setClipboardData({
				data: text,
				success: () => {
					uni.showToast({
						title: '内容已复制',
						icon: 'success'
					})
				},
				fail: () => {
					uni.showToast({
						title: '复制失败',
						icon: 'error'
					})
				}
			})
		},

		// 打开链接
		openLink(url) {
			// #ifdef H5
			window.open(url, '_blank')
			// #endif

			// #ifdef APP-PLUS
			plus.runtime.openURL(url)
			// #endif

			// #ifdef MP
			uni.setClipboardData({
				data: url,
				success: () => {
					uni.showModal({
						title: '提示',
						content: '链接已复制到剪贴板，请在浏览器中打开',
						showCancel: false
					})
				}
			})
			// #endif
		},

		// 图片加载成功处理
		onImageLoad(e) {
			console.log('✅ 图片加载成功:', e)
			console.log('✅ 图片URL:', this.workflowResult)
			this.imageLoadingState = 'loaded'
		},

		// 图片加载错误处理
		onImageLoadError(e) {
			console.error('❌ 图片加载失败:', e)
			console.error('❌ 失败的图片URL:', this.workflowResult)
			this.imageLoadingState = 'error'
			uni.showToast({
				title: '图片加载失败',
				icon: 'error',
				duration: 2000
			})
		},

		// 重试图片加载
		retryImageLoad() {
			console.log('重试加载图片:', this.workflowResult)
			this.imageLoadingState = 'loading'
			// 强制重新加载图片
			this.$forceUpdate()
		},

		// 预览图片
		previewImage(imageUrl) {
			console.log('预览图片:', imageUrl)
			uni.previewImage({
				urls: [imageUrl],
				current: imageUrl,
				fail: (err) => {
					console.error('预览图片失败:', err)
					uni.showToast({
						title: '预览失败',
						icon: 'error'
					})
				}
			})
		},

		// 兼容旧的图片错误处理方法
		onImageError(e) {
			this.onImageLoadError(e)
		},

		// 设置工作流结果并重置图片状态
		setWorkflowResult(result) {
			console.log('🔍 原始工作流结果:', result)
			console.log('🔍 结果类型:', typeof result)

			// 解析结果中的链接
			let processedResult = result
			let extractedLinks = []

			if (typeof result === 'string') {
				// 提取双引号内的链接
				const quotedUrlRegex = /"(https?:\/\/[^"]+)"/g
				let match
				while ((match = quotedUrlRegex.exec(result)) !== null) {
					extractedLinks.push(match[1])
				}

				// 如果找到了链接，使用链接数组
				if (extractedLinks.length > 0) {
					processedResult = extractedLinks
					console.log('🔗 提取到的链接:', extractedLinks)
				} else if (result.includes('![')) {
					// 兼容Markdown格式
					const markdownImageRegex = /!\[.*?\]\((https?:\/\/[^\)]+)\)/g
					const matches = result.match(markdownImageRegex)
					if (matches && matches.length > 0) {
						const urlMatch = matches[0].match(/\((https?:\/\/[^\)]+)\)/)
						if (urlMatch && urlMatch[1]) {
							processedResult = urlMatch[1]
							console.log('🖼️ 从Markdown中提取图片URL:', processedResult)
						}
					}
				}
			}

			this.workflowResult = processedResult

			// 重置图片加载状态
			const isImage = Array.isArray(processedResult) ?
				this.isImageUrl(processedResult[0]) :
				this.isImageUrl(processedResult)
			console.log('🔍 是否为图片URL:', isImage, '处理后的结果:', processedResult)

			if (isImage) {
				this.imageLoadingState = 'loading'
			} else {
				this.imageLoadingState = 'idle'
			}
			console.log('✅ 设置工作流结果完成 - 结果:', processedResult, '图片状态:', this.imageLoadingState)

			// 注释掉额外的记录保存，因为后端已经在工作流执行成功后自动保存了记录
			// this.saveWorkflowRecord(processedResult)
		},

		// 保存工作流执行记录
		async saveWorkflowRecord(result) {
			try {
				// 检查用户登录状态
				if (!userStore.isLoggedIn()) {
					console.log('⚠️ 用户未登录，跳过保存工作流记录')
					return
				}

				const recordData = {
					taskSource: 'UniApp前端',
					taskType: '工作流',
					taskTypeDetail: this.currentTheme?.title || '工作流执行',
					workflowId: this.currentTheme?.workflowId,
					workflowName: this.currentTheme?.title || '未知工作流',
					consumption: this.currentTheme?.consumption || this.workflowConsumption || 1000,
					status: result && !result.includes('失败') && !result.includes('错误') ? '已完成' : '失败',
					logs: `工作流执行${result && !result.includes('失败') ? '成功' : '失败'}`,
					input: JSON.stringify(this.workflowInputs),
					output: result || '无输出',
					instructions: `执行工作流 ${this.currentTheme?.workflowId}，参数：${JSON.stringify(this.workflowInputs)}`
				}

				console.log('📝 保存工作流记录:', recordData)

				await workflowHistoryApi.createWorkflowRecord(recordData)
				console.log('✅ 工作流记录保存成功')

			} catch (error) {
				console.error('❌ 保存工作流记录失败:', error)
				// 不影响主流程，只记录错误
			}
		},

		// 视频加载错误处理
		onVideoError(e) {
			console.error('视频加载失败:', e)
			uni.showToast({
				title: '视频加载失败',
				icon: 'error'
			})
		},

		// ========== 原有方法 ==========

		// 初始化图片URL
		initializeImageUrls() {
			// 处理图片内容URL
			if (this.mediaContents.image.content && this.mediaContents.image.content.startsWith('/')) {
				this.mediaContents.image.content = IMAGE_BASE_URL + this.mediaContents.image.content
			}
			console.log('✅ 初始化图片URL完成:', {
				imageContent: this.mediaContents.image.content
			})
		},





		// 处理多媒体内容点击
		handleMediaClick(type) {
			console.log('点击多媒体内容:', type)

			const media = this.mediaContents[type]
			if (!media || !media.enabled) {
				return
			}

			if (type === 'video') {
				// 处理案例视频展示
				this.playVideo(media)
			} else if (type === 'image') {
				// 处理案例图片展示
				this.previewImage(media)
			} else if (type === 'text') {
				// 处理文字教学
				this.expandText(media)
			}
		},

		// 播放案例视频
		playVideo(media) {
			uni.showToast({
				title: '播放' + media.title,
				icon: 'none'
			})
			// 这里可以集成视频播放器或跳转到视频页面
		},

		// 预览案例图片
		previewImage(media) {
			uni.previewImage({
				urls: [media.content],
				current: media.content
			})
		},

		// 展开文字教学
		expandText(media) {
			uni.showModal({
				title: media.title,
				content: media.content,
				showCancel: false,
				confirmText: '知道了'
			})
		},

		// 更新多媒体内容配置（从后端数据）
		updateMediaContents(agentData) {
			console.log('🔄 更新多媒体内容配置，后端数据:', agentData)
			if (!agentData) return

			// 从 introContents 字段中获取多媒体内容
			let introContents = agentData.introContents
			console.log('📋 原始 introContents 数据:', introContents)
			console.log('📋 introContents 类型:', typeof introContents)

			// 处理数组格式的 introContents
			let processedContents = {}

			if (Array.isArray(introContents)) {
				console.log('📋 introContents 是数组，开始处理...')
				// 将数组转换为对象格式
				introContents.forEach((item, index) => {
					console.log(`📋 处理第 ${index + 1} 项:`, item)
					if (item && item.type && item.value) {
						processedContents[item.type] = item.value
						console.log(`✅ 添加 ${item.type}:`, item.value)
					}
				})
			} else if (typeof introContents === 'string') {
				// 如果是字符串，尝试解析为 JSON
				if (introContents.trim() === '') {
					processedContents = {}
					console.log('📋 introContents 为空字符串，设为空对象')
				} else {
					try {
						const parsed = JSON.parse(introContents)
						if (Array.isArray(parsed)) {
							parsed.forEach((item) => {
								if (item && item.type && item.value) {
									processedContents[item.type] = item.value
								}
							})
						} else {
							processedContents = parsed
						}
						console.log('📋 解析后的 introContents:', processedContents)
					} catch (error) {
						console.error('❌ 解析 introContents JSON 失败:', error)
						processedContents = {}
					}
				}
			} else if (introContents && typeof introContents === 'object') {
				// 如果已经是对象，直接使用
				processedContents = introContents
			} else {
				// 其他情况设为空对象
				processedContents = {}
			}

			console.log('📋 最终处理的内容:', processedContents)

			// 检查是否有数据
			const isEmpty = Object.keys(processedContents).length === 0
			console.log('📋 处理后的内容是否为空:', isEmpty)

			// 更新案例视频展示
			console.log('🔍 检查 video 字段:', processedContents.video, '类型:', typeof processedContents.video)
			if (processedContents.video && processedContents.video.trim() !== '') {
				console.log('✅ 有视频数据，启用视频展示:', processedContents.video)
				this.mediaContents.video.enabled = true
				// 处理视频URL - 如果是相对路径则添加 IMAGE_BASE_URL
				let videoUrl = processedContents.video
				if (videoUrl.startsWith('/')) {
					videoUrl = IMAGE_BASE_URL + videoUrl
				}
				this.mediaContents.video.content = videoUrl
				console.log('📹 最终视频URL:', videoUrl)
			} else {
				console.log('❌ 无视频数据，禁用视频展示，video 字段:', processedContents.video)
				this.mediaContents.video.enabled = false
			}

			// 更新案例图片展示
			console.log('🔍 检查 image 字段:', processedContents.image, '类型:', typeof processedContents.image)
			if (processedContents.image && processedContents.image.trim() !== '') {
				console.log('✅ 有图片数据，启用图片展示:', processedContents.image)
				this.mediaContents.image.enabled = true
				// 处理图片URL，如果是相对路径则添加图片服务器地址
				if (processedContents.image.startsWith('/')) {
					this.mediaContents.image.content = IMAGE_BASE_URL + processedContents.image
				} else {
					this.mediaContents.image.content = processedContents.image
				}
			} else {
				console.log('❌ 无图片数据，禁用图片展示，image 字段:', processedContents.image)
				this.mediaContents.image.enabled = false
			}

			// 更新文字教学
			console.log('🔍 检查 text 字段:', processedContents.text, '类型:', typeof processedContents.text)
			if (processedContents.text && processedContents.text.trim() !== '') {
				console.log('✅ 有后端文字数据，启用文字教学:', processedContents.text)
				this.mediaContents.text.enabled = true
				this.mediaContents.text.content = processedContents.text
			} else {
				console.log('❌ 无后端文字数据，禁用文字教学')
				this.mediaContents.text.enabled = false
			}
			console.log('🔍 最终 mediaContents.text 状态:', this.mediaContents.text)

			console.log('📋 多媒体内容配置更新完成:', this.mediaContents)

			// 强制触发视图更新
			this.$forceUpdate()
		},

		// 初始化聊天
		async initializeChat() {
			try {
				this.isLoading = true
				this.errorMessage = ''

				console.log('=== 开始初始化聊天 ===')
				console.log('当前应用ID:', this.appId)
				console.log('API基础URL:', API_BASE_URL)

				// 检查网络连接
				const isConnected = await this.checkNetworkConnection()
				if (!isConnected) {
					throw new Error('网络连接不可用，请检查网络设置')
				}

				// 使用重试机制获取智能体主题配置
				await this.callApiWithRetry(async () => {
					console.log('=== 开始获取智能体主题配置 ===')
					console.log('传入的应用ID:', this.appId)

					// 如果有传入的应用ID，优先根据ID获取对应的智能体配置
					if (this.appId) {
						console.log('🔍 根据应用ID获取智能体配置:', this.appId)
						try {
							this.currentTheme = await agentThemeManager.apiClient.getAgentThemeById(this.appId)
							console.log('✅ 成功获取智能体主题配置:', this.currentTheme)
							console.log('主题配置类型:', typeof this.currentTheme)
							console.log('主题配置详情:', JSON.stringify(this.currentTheme, null, 2))
						} catch (error) {
							console.warn('❌ 根据应用ID获取智能体配置失败:', error)
							console.warn('错误详情:', {
								message: error.message,
								name: error.name,
								stack: error.stack
							})
							this.currentTheme = null
						}
					} else {
						console.warn('⚠️ 未提供应用ID，将使用默认配置')
					}

					// 如果没有获取到配置，则使用页面默认配置
					if (!this.currentTheme) {
						console.log('🔄 使用页面默认配置')
						const currentPage = '/pages/index/znt'
						this.currentTheme = await agentThemeManager.getThemeForPage(currentPage)
						console.log('默认配置结果:', this.currentTheme)
					}

					if (this.currentTheme) {
						this.botId = agentThemeManager.apiClient.getBotIdFromTheme(this.currentTheme)
						console.log('✅ 智能体主题配置获取成功')
						console.log('最终主题配置:', this.currentTheme)
						console.log('提取的智能体ID:', this.botId)

						// 更新页面信息
						console.log('🔄 准备调用 updatePageInfo')
						this.updatePageInfo()
						console.log('✅ updatePageInfo 调用完成')
					} else {
						console.warn('⚠️ 未找到匹配的智能体主题，使用默认配置')
						// 使用您实际的智能体ID
						this.botId = '7491537356286459913' // 您的智能体ID
					}

					// 确保botId不为空
					if (!this.botId) {
						console.warn('⚠️ botId为空，使用默认ID')
						this.botId = '7491537356286459913' // 您的智能体ID
					}

					console.log('🎯 最终确定的智能体ID:', this.botId)
					console.log('=== 智能体主题配置获取结束 ===')
				})

				// 检查Coze API是否已初始化
				if (!cozeApi.isInitialized()) {
					console.log('Coze API未初始化，尝试从系统配置加载')
					await this.callApiWithRetry(async () => {
						await cozeApi.loadFromSystemConfig()
					})
				}

				// 更新API初始化状态
				this.isApiInitialized = cozeApi.isInitialized()

				// 重置重试计数
				this.resetRetryCount()

				console.log('聊天初始化成功')

				// 预加载视频尺寸信息
				this.preloadVideoInfo()

			} catch (error) {
				console.error('初始化聊天失败:', error)
				console.error('错误详情:', {
					message: error.message,
					name: error.name,
					stack: error.stack,
					appId: this.appId,
					apiBaseUrl: API_BASE_URL
				})
				this.errorMessage = this.getErrorMessage(error)
				this.showErrorToast('初始化失败: ' + error.message)
			} finally {
				this.isLoading = false
			}
		},







		// 格式化时间
		formatTime(timestamp) {
			const date = new Date(timestamp)
			const now = new Date()
			const diff = now - date

			if (diff < 60000) { // 1分钟内
				return '刚刚'
			} else if (diff < 3600000) { // 1小时内
				return Math.floor(diff / 60000) + '分钟前'
			} else if (diff < 86400000) { // 24小时内
				return Math.floor(diff / 3600000) + '小时前'
			} else {
				return date.toLocaleDateString()
			}
		},

		// 检查是否是图片消息（Markdown格式）
		isImageMessage(content) {
			if (!content) return false
			// 检查Markdown图片格式: ![alt](url)
			const markdownImagePattern = /!\[.*?\]\((https?:\/\/.*?\.(png|jpg|jpeg|gif|webp)(\?.*)?)\)/i
			return markdownImagePattern.test(content)
		},

		// 检查是否是纯URL消息
		isUrlMessage(content) {
			if (!content) return false
			// 检查是否是纯图片URL
			const imageUrlPattern = /^https?:\/\/.*\.(png|jpg|jpeg|gif|webp)(\?.*)?$/i
			return imageUrlPattern.test(content.trim())
		},

		// 从Markdown格式中提取图片URL
		extractImageUrl(content) {
			if (!content) return ''

			// 先尝试Markdown格式
			const markdownMatch = content.match(/!\[.*?\]\((https?:\/\/.*?\.(png|jpg|jpeg|gif|webp)(\?.*)?)\)/i)
			if (markdownMatch) {
				return markdownMatch[1]
			}

			// 如果是纯URL，直接返回
			const imageUrlPattern = /^https?:\/\/.*\.(png|jpg|jpeg|gif|webp)(\?.*)?$/i
			if (imageUrlPattern.test(content.trim())) {
				return content.trim()
			}

			return ''
		},

		// 预览图片
		previewImage(imageUrl) {
			if (!imageUrl) return

			uni.previewImage({
				urls: [imageUrl],
				current: imageUrl,
				fail: (err) => {
					console.error('预览图片失败:', err)
					uni.showToast({
						title: '预览失败',
						icon: 'none'
					})
				}
			})
		},

		// 复制图片URL
		copyImageUrl(imageUrl) {
			this.copyToClipboard(imageUrl)
		},



		// 打开历史记录弹窗
		async openHistoryModal() {
			console.log('打开历史记录弹窗')

			// 检查用户登录状态
			if (!userStore.isLoggedIn()) {
				uni.showModal({
					title: '提示',
					content: '请先登录查看工作流历史',
					confirmText: '去登录',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/login'
							})
						}
					}
				})
				return
			}

			this.showHistoryModal = true
			this.historyPage = 1
			this.hasMoreHistory = true
			await this.loadWorkflowHistory()
		},

		// 关闭历史记录弹窗
		closeHistoryModal() {
			this.showHistoryModal = false
			this.historyList = []
			console.log('关闭历史记录弹窗')
		},

		// 加载工作流历史
		async loadWorkflowHistory() {
			if (this.historyLoading) return

			console.log('🔍 开始加载工作流历史记录...')
			this.historyLoading = true
			try {
				console.log('📞 调用workflowHistoryApi.getWorkflowHistory')
				const response = await workflowHistoryApi.getWorkflowHistory({
					page: this.historyPage,
					pageSize: 10
				})

				console.log('📥 收到工作流历史响应:', response)

				if (response.code === 200) {
					const rawRecords = response.data.records || []
					console.log('📊 原始记录数量:', rawRecords.length)

					const formattedRecords = workflowHistoryApi.formatWorkflowRecords(rawRecords)
					console.log('📋 格式化记录数量:', formattedRecords.length)

					if (this.historyPage === 1) {
						this.historyList = formattedRecords
					} else {
						this.historyList.push(...formattedRecords)
					}

					// 检查是否还有更多数据
					this.hasMoreHistory = rawRecords.length === 10
					console.log('✅ 工作流历史加载成功，总记录数:', this.historyList.length)
				} else {
					console.error('❌ 工作流历史响应错误:', response)
					throw new Error(`响应错误: ${response.code} - ${response.message}`)
				}
			} catch (error) {
				console.error('❌ 加载工作流历史失败:', error)
				console.error('❌ 错误详情:', {
					message: error.message,
					stack: error.stack
				})

				// 确保在错误情况下historyList为空数组，显示空状态
				if (this.historyPage === 1) {
					this.historyList = []
				}
				uni.showToast({
					title: '加载失败: ' + error.message,
					icon: 'none',
					duration: 3000
				})
			} finally {
				this.historyLoading = false
				console.log('🏁 工作流历史加载完成')
			}
		},

		// 加载更多历史记录
		async loadMoreHistory() {
			if (!this.hasMoreHistory || this.historyLoading) return

			this.historyPage++
			await this.loadWorkflowHistory()
		},

		// 获取当前工作流的消耗点数
		getWorkflowConsumption() {
			if (this.currentTheme && typeof this.currentTheme.consumption === 'number' && this.currentTheme.consumption >= 0) {
				return this.currentTheme.consumption
			} else if (this.workflowConsumption && this.workflowConsumption !== 1000) {
				return this.workflowConsumption
			} else {
				return 1000 // 默认值
			}
		},

		// 提取输入参数的值（不显示参数名称）
		getInputValues(inputStr) {
			if (!inputStr) return ''

			try {
				// 尝试解析JSON格式的输入
				const inputObj = JSON.parse(inputStr)
				if (typeof inputObj === 'object' && inputObj !== null) {
					// 提取所有值并用逗号分隔
					const values = Object.values(inputObj).filter(value => value && value.toString().trim() !== '')
					return values.join(', ')
				}
			} catch (e) {
				// 如果不是JSON格式，直接返回原字符串
				return inputStr
			}

			return inputStr
		},

		// 提取输出中双引号内的链接
		getOutputLinks(outputStr) {
			if (!outputStr) return []

			const links = []
			// 使用正则表达式提取双引号内的链接
			const quotedUrlRegex = /"(https?:\/\/[^"]+)"/g
			let match
			while ((match = quotedUrlRegex.exec(outputStr)) !== null) {
				// 清理链接中的转义字符
				const cleanLink = match[1].replace(/\\/g, '')
				links.push(cleanLink)
			}

			return links
		},

		// 截断显示链接（保留域名和文件名部分）
		truncateLink(link) {
			if (!link) return ''

			try {
				const url = new URL(link)
				const pathname = url.pathname
				const filename = pathname.split('/').pop() || ''

				// 如果文件名太长，截断显示
				if (filename.length > 30) {
					return `${url.hostname}/...${filename.substring(filename.length - 20)}`
				} else if (pathname.length > 40) {
					return `${url.hostname}/.../${filename}`
				} else {
					return `${url.hostname}${pathname}`
				}
			} catch (e) {
				// 如果URL解析失败，直接截断
				if (link.length > 50) {
					return link.substring(0, 30) + '...' + link.substring(link.length - 15)
				}
				return link
			}
		},

		// 打开分享弹窗
		async openShareModal() {
			console.log('打开分享弹窗')

			// 检查用户登录状态
			if (!userStore.isLoggedIn()) {
				uni.showModal({
					title: '提示',
					content: '请先登录使用分享功能',
					confirmText: '去登录',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/login'
							})
						}
					}
				})
				return
			}

			this.showShareModal = true
			this.shareCode = ''

			// 加载分享配置
			try {
				const response = await shareApi.getShareConfig()
				if (response.code === 200) {
					this.shareConfig = response.data
				}
			} catch (error) {
				console.error('加载分享配置失败:', error)
			}
		},

		// 关闭分享弹窗
		closeShareModal() {
			this.showShareModal = false
			this.shareCode = ''
			console.log('关闭分享弹窗')
		},

		// 微信分享
		shareToWechat() {
			console.log('微信分享')

			// 获取分享配置
			const shareTitle = this.shareConfig?.share_title || 'AI智能体使用邀请'
			const shareDesc = this.shareConfig?.share_description || '注册即可获得额外免费使用次数，快来体验智能AI助手！'
			const shareImageUrl = this.shareConfig?.share_image_url || ''

			// 构建分享路径，包含邀请人信息
			const currentUser = userStore.getCurrentUser()
			const sharePath = `/pages/index/znt?inviter=${currentUser.id}&botId=${this.botId}`

			const shareConfig = shareApi.getWechatShareConfig({
				title: shareTitle,
				desc: shareDesc,
				imageUrl: shareImageUrl,
				path: sharePath
			})

			// 微信小程序分享
			// #ifdef MP-WEIXIN
			uni.showShareMenu({
				withShareTicket: true,
				success: () => {
					console.log('分享菜单显示成功')
				}
			})
			// #endif

			// 其他平台提示
			// #ifndef MP-WEIXIN
			uni.showToast({
				title: '请在微信小程序中使用分享功能',
				icon: 'none'
			})
			// #endif
		},

		// 生成分享邀请码
		async generateShareCode() {
			console.log('生成分享邀请码')

			try {
				uni.showLoading({
					title: '生成中...'
				})

				const response = await shareApi.generateInviteCode({
					type: 'agent',
					targetId: this.botId || 'default'
				})

				if (response.code === 200) {
					this.shareCode = response.data.inviteCode
					uni.showToast({
						title: '邀请码生成成功',
						icon: 'success'
					})
				} else {
					throw new Error(response.message || '生成失败')
				}
			} catch (error) {
				console.error('生成邀请码失败:', error)
				uni.showToast({
					title: '生成失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		// 复制分享链接
		copyShareLink() {
			console.log('复制分享链接')

			const currentUser = userStore.getCurrentUser()
			const shareUrl = `${API_BASE_URL}/share?inviter=${currentUser.id}&botId=${this.botId}`

			this.copyToClipboard(shareUrl)
		},

		// 复制邀请码
		copyShareCode() {
			if (!this.shareCode) {
				uni.showToast({
					title: '请先生成邀请码',
					icon: 'none'
				})
				return
			}

			this.copyToClipboard(this.shareCode)
		},

		// 截取文本
		truncateText(text, maxLength) {
			if (!text) return ''
			if (text.length <= maxLength) return text
			return text.substring(0, maxLength) + '...'
		},

		// 处理邀请人关系
		async handleInviterRelation(inviterId) {
			try {
				console.log('处理邀请人关系:', inviterId)
				// 这里可以调用API建立邀请关系
				// 暂时只记录日志，具体实现可以根据需要添加
			} catch (error) {
				console.error('处理邀请人关系失败:', error)
			}
		},

		// 处理邀请码访问
		async handleInviteCodeAccess(inviteCode) {
			try {
				console.log('处理邀请码访问:', inviteCode)

				const response = await shareApi.handleInviteCode(inviteCode)
				if (response.code === 200) {
					console.log('邀请码处理成功:', response.data)

					// 如果需要登录，提示用户
					if (response.data.needLogin) {
						uni.showModal({
							title: '欢迎使用',
							content: '请登录以获得邀请奖励',
							confirmText: '去登录',
							cancelText: '稍后',
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/auth/login'
									})
								}
							}
						})
					} else {
						uni.showToast({
							title: '欢迎使用！',
							icon: 'success'
						})
					}
				}
			} catch (error) {
				console.error('处理邀请码失败:', error)
			}
		},





		// 复制到剪贴板
		copyToClipboard(text) {
			if (!text) return

			uni.setClipboardData({
				data: text,
				success: () => {
					uni.showToast({
						title: '已复制到剪贴板',
						icon: 'success',
						duration: 1500
					})
				},
				fail: (err) => {
					console.error('复制失败:', err)
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					})
				}
			})
		},

		// 设置网络状态监听
		setupNetworkListener() {
			// 获取当前网络状态
			uni.getNetworkType({
				success: (res) => {
					this.isOnline = res.networkType !== 'none'
				}
			})

			// 监听网络状态变化
			uni.onNetworkStatusChange((res) => {
				this.isOnline = res.isConnected

				if (!res.isConnected) {
					uni.showToast({
						title: '网络连接已断开',
						icon: 'error'
					})
				} else {
					uni.showToast({
						title: '网络连接已恢复',
						icon: 'success'
					})

					// 网络恢复后，如果有错误状态，尝试重新初始化
					if (this.errorMessage) {
						this.retryInitialize()
					}
				}
			})
		},

		// 重试初始化
		async retryInitialize() {
			if (this.retryCount < this.maxRetries) {
				this.retryCount++
				console.log(`重试初始化，第${this.retryCount}次`)
				await this.initializeChat()
			} else {
				this.errorMessage = '初始化失败次数过多，请检查网络连接后手动重试'
			}
		},

		// 重置重试计数
		resetRetryCount() {
			this.retryCount = 0
		},

		// 检查网络连接
		async checkNetworkConnection() {
			return new Promise((resolve) => {
				uni.getNetworkType({
					success: (res) => {
						const isConnected = res.networkType !== 'none'
						this.isOnline = isConnected
						resolve(isConnected)
					},
					fail: () => {
						this.isOnline = false
						resolve(false)
					}
				})
			})
		},

		// 带重试的API调用
		async callApiWithRetry(apiCall, maxRetries = 2) {
			let lastError = null

			for (let i = 0; i <= maxRetries; i++) {
				try {
					// 检查网络连接
					const isConnected = await this.checkNetworkConnection()
					if (!isConnected) {
						throw new Error('网络连接不可用')
					}

					// 执行API调用
					const result = await apiCall()
					return result
				} catch (error) {
					lastError = error
					console.error(`API调用失败，第${i + 1}次尝试:`, error)

					// 如果不是最后一次重试，等待一段时间后重试
					if (i < maxRetries) {
						await this.delay(1000 * (i + 1)) // 递增延迟
					}
				}
			}

			throw lastError
		},

		// 延迟函数
		delay(ms) {
			return new Promise(resolve => setTimeout(resolve, ms))
		},

		// 格式化时间
		formatTime(timestamp) {
			if (!timestamp) return ''
			const date = new Date(timestamp)
			const now = new Date()
			const diff = now - date

			// 如果是今天
			if (diff < 24 * 60 * 60 * 1000) {
				return date.toLocaleTimeString('zh-CN', {
					hour: '2-digit',
					minute: '2-digit'
				})
			}

			// 如果是昨天或更早
			return date.toLocaleDateString('zh-CN', {
				month: '2-digit',
				day: '2-digit',
				hour: '2-digit',
				minute: '2-digit'
			})
		},

		// 根据智能体配置更新页面信息
		updatePageInfo() {
			console.log('=== 开始更新页面信息 ===')
			console.log('当前主题数据:', this.currentTheme)
			console.log('当前主题类型:', typeof this.currentTheme)

			if (this.currentTheme) {
				console.log('✅ 有主题数据，开始更新页面信息')
				console.log('主题详细信息:', JSON.stringify(this.currentTheme, null, 2))

				// 更新智能体标题
				if (this.currentTheme.title) {
					console.log('更新标题:', this.currentTheme.title)
					this.agentTitle = this.currentTheme.title
					this.welcomeTitle = `${this.currentTheme.title}`
				} else {
					console.log('⚠️ 主题中没有title字段')
				}

				// 更新智能体描述
				if (this.currentTheme.description) {
					console.log('更新描述:', this.currentTheme.description)
					this.welcomeDesc = this.currentTheme.description
				} else {
					console.log('⚠️ 主题中没有description字段')
				}

				// 更新智能体头像
				if (this.currentTheme.icon) {
					console.log('更新头像:', this.currentTheme.icon)
					// 处理图片URL，如果是相对路径则添加图片服务器地址
					if (this.currentTheme.icon.startsWith('/')) {
						this.agentAvatar = IMAGE_BASE_URL + this.currentTheme.icon
					} else {
						this.agentAvatar = this.currentTheme.icon
					}
					console.log('最终头像URL:', this.agentAvatar)
				} else {
					console.log('⚠️ 主题中没有icon字段')
				}

				// 更新副标题（保持默认的消息提分信息）
				// 不显示类型信息，保持原有的副标题

				// 更新消息扣除点数
				if (this.currentTheme.consumption !== undefined && this.currentTheme.consumption !== null) {
					console.log('更新消息扣除点数:', this.currentTheme.consumption)
					this.consumption = this.currentTheme.consumption
				} else {
					console.log('⚠️ 主题中没有consumption字段，使用默认值1')
					this.consumption = 1
				}

				// 更新多媒体内容配置
				console.log('🔄 更新多媒体内容配置')
				this.updateMediaContents(this.currentTheme)

				// 更新工作流相关配置
				console.log('🔄 更新工作流相关配置')
				if (this.currentTheme.workflowId) {
					console.log('✅ 检测到工作流ID:', this.currentTheme.workflowId)
					// 更新工作流消耗点数
					if (this.currentTheme.consumption !== undefined && this.currentTheme.consumption !== null) {
						this.workflowConsumption = this.currentTheme.consumption
						console.log('✅ 更新工作流消耗点数:', this.workflowConsumption)
					}
				} else {
					console.log('⚠️ 主题中没有workflowId字段，这不是工作流主题')
				}

				console.log('✅ 页面信息更新完成:', {
					title: this.agentTitle,
					subtitle: this.agentSubtitle,
					avatar: this.agentAvatar,
					welcomeTitle: this.welcomeTitle,
					welcomeDesc: this.welcomeDesc,
					consumption: this.consumption,
					workflowConsumption: this.workflowConsumption,
					workflowId: this.currentTheme?.workflowId,
					mediaContents: this.mediaContents
				})
			} else {
				console.log('❌ 没有主题数据，无法更新页面信息')
			}
			console.log('=== 页面信息更新结束 ===')
		},

		// 获取用户点数信息
		async fetchUserPoints() {
			try {
				console.log('🔄 开始获取用户点数信息')

				// 获取存储的token
				const token = uni.getStorageSync('token')
				console.log('获取到的token:', token ? '已获取' : '未获取')

				// 如果没有token，设置为0点数提示用户登录
				if (!token) {
					console.warn('⚠️ 未找到认证token，用户未登录')
					this.remainingPoints = 0 // 未登录用户点数为0
					return
				}

				// 调用后端API获取用户信息
				const response = await uni.request({
					url: `${API_BASE_URL}/api/members/me`,
					method: 'GET',
					header: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${token}`
					}
				})

				console.log('用户信息API响应:', response)

				if (response.statusCode === 200 && response.data) {
					// 处理会员API的响应格式
					if (response.data.success && response.data.data) {
						const userData = response.data.data
						console.log('✅ 获取到会员信息:', userData)

						// 更新剩余点数 - 会员系统使用points字段
						if (userData.points !== undefined) {
							this.remainingPoints = userData.points || 0
							console.log('✅ 更新用户剩余点数(points):', this.remainingPoints)
						} else if (userData.balance !== undefined) {
							this.remainingPoints = userData.balance || 0
							console.log('✅ 更新用户剩余点数(balance):', this.remainingPoints)
						} else {
							console.log('⚠️ 用户数据中没有找到点数字段，设置为0')
							this.remainingPoints = 0 // 数据异常时设置为0
						}
					} else {
						console.warn('⚠️ API响应格式异常:', response.data)
						this.remainingPoints = 0 // API响应异常时设置为0
					}
				} else {
					console.warn('⚠️ 获取用户信息失败，设置为0')
					this.remainingPoints = 0 // API调用失败时设置为0
				}
			} catch (error) {
				console.error('❌ 获取用户点数失败:', error)
				this.remainingPoints = 0 // 错误时设置为0
				// 不显示错误提示，避免影响用户体验
			}
		},

		// 检查用户登录状态
		checkUserLoginStatus() {
			try {
				console.log('🔄 检查用户登录状态')

				// 检查是否已登录
				this.isLoggedIn = userStore.isLoggedIn()
				this.userInfo = userStore.getCurrentUser()

				console.log('用户登录状态:', this.isLoggedIn)
				console.log('用户信息:', this.userInfo)

				if (this.isLoggedIn && this.userInfo) {
					console.log('✅ 用户已登录:', this.userInfo.username)
					// 如果已登录，获取最新的用户点数信息
					this.fetchUserPoints()
				} else {
					console.log('⚠️ 用户未登录，点数设置为0')
					// 未登录时点数为0，促使用户登录
					this.remainingPoints = 0
				}
			} catch (error) {
				console.error('❌ 检查用户登录状态失败:', error)
				this.isLoggedIn = false
				this.userInfo = null
				this.remainingPoints = 0 // 错误时设置为0
			}
		},

		// 显示登录界面
		showLoginModal() {
			// 这里可以跳转到登录页面或显示登录弹窗
			uni.navigateTo({
				url: '/pages/auth/login'
			})
		},

		// 跳转到会员套餐页面
		navigateToMembershipPage() {
			console.log('🛒 跳转到会员套餐页面')

			// 检查用户是否已登录
			const token = uni.getStorageSync('token')
			if (!token) {
				// 未登录，显示登录弹窗
				this.showLoginModalFlag = true
				return
			}

			// 已登录，跳转到套餐页面
			try {
				uni.navigateTo({
					url: '/pages/chat/index'
				})
			} catch (error) {
				console.error('跳转到套餐页面失败:', error)
				// 如果套餐页面不存在，可以跳转到其他相关页面
				uni.showToast({
					title: '页面暂未开放',
					icon: 'none'
				})
			}
		},

		// ========== 自定义弹窗相关方法 ==========

		// 显示点数不足弹窗
		showPointsInsufficientModal(requiredPoints) {
			console.log('🔔 显示自定义点数不足弹窗')
			this.requiredPointsForModal = requiredPoints
			this.showPointsModal = true
		},

		// 关闭点数不足弹窗
		closePointsModal() {
			console.log('❌ 关闭点数不足弹窗')
			this.showPointsModal = false
			this.requiredPointsForModal = 0
		},

		// 处理购买套餐按钮点击
		handleBuyPackage() {
			console.log('🛒 用户点击购买套餐')
			this.closePointsModal()
			this.navigateToMembershipPage()
		},

		// 显示错误提示
		showErrorToast(message, duration = 2000) {
			uni.showToast({
				title: message,
				icon: 'error',
				duration: duration
			})
		},

		// 显示成功提示
		showSuccessToast(message, duration = 1500) {
			uni.showToast({
				title: message,
				icon: 'success',
				duration: duration
			})
		},

		// 显示自定义高层级Toast
		showCustomToast(message, type = 'success', duration = 2000) {
			this.copyToastMessage = message
			this.copyToastType = type
			this.showCopyToast = true

			// 自动隐藏
			setTimeout(() => {
				this.showCopyToast = false
			}, duration)
		},



		// 获取友好的错误消息
		getErrorMessage(error) {
			if (!error) return '未知错误'

			const message = error.message || error.toString()

			// 网络相关错误
			if (message.includes('网络') || message.includes('network') || message.includes('timeout')) {
				return '网络连接异常，请检查网络设置'
			}

			// 认证相关错误
			if (message.includes('401') || message.includes('unauthorized') || message.includes('token')) {
				return '认证失败，请重新配置'
			}

			// 服务器错误
			if (message.includes('500') || message.includes('server') || message.includes('internal')) {
				return '服务器暂时不可用，请稍后重试'
			}

			// 配置错误
			if (message.includes('配置') || message.includes('config')) {
				return '配置信息有误，请检查设置'
			}

			// 智能体相关错误
			if (message.includes('bot') || message.includes('智能体')) {
				return '智能体服务异常，请稍后重试'
			}

			// 默认错误消息
			return '服务暂时不可用，请稍后重试'
		},

		// 视频加载完成事件
		onVideoLoaded(event) {
			console.log('视频loadedmetadata事件:', event)
			this.calculateVideoHeight(event)
		},

		// 视频可以播放时
		onVideoCanPlay(event) {
			console.log('视频canplay事件:', event)
			this.calculateVideoHeight(event)
		},

		// 视频时间更新时（作为备用方案）
		onVideoTimeUpdate(event) {
			// 只在第一次时间更新时计算，避免重复计算
			if (this.videoHeight === 250) {
				console.log('视频timeupdate事件:', event)
				this.calculateVideoHeight(event)
			}
		},

		// 计算视频高度的通用方法
		calculateVideoHeight(event) {
			console.log('计算视频高度，事件数据:', event)

			// 尝试从不同的事件属性中获取视频尺寸
			let videoWidth, videoHeight

			if (event && event.detail) {
				videoWidth = event.detail.videoWidth || event.detail.width
				videoHeight = event.detail.videoHeight || event.detail.height
			}

			console.log('获取到的视频尺寸:', { videoWidth, videoHeight })

			if (videoWidth && videoHeight && videoWidth > 0 && videoHeight > 0) {
				// 计算容器宽度（屏幕宽度减去边距）
				const systemInfo = uni.getSystemInfoSync()
				const containerWidth = systemInfo.windowWidth - 40 // 减去左右边距

				// 根据视频比例计算自适应高度
				const aspectRatio = videoHeight / videoWidth
				const calculatedHeight = containerWidth * aspectRatio

				// 只有计算出的高度合理时才更新
				if (calculatedHeight > 50 && calculatedHeight < 1000) {
					this.videoHeight = Math.round(calculatedHeight)

					console.log('✅ 视频尺寸调整成功:', {
						原始尺寸: `${videoWidth}x${videoHeight}`,
						屏幕宽度: systemInfo.windowWidth,
						容器宽度: containerWidth,
						宽高比: aspectRatio,
						计算高度: calculatedHeight,
						最终高度: this.videoHeight
					})

					// 强制更新视图
					this.$forceUpdate()
				} else {
					console.log('❌ 计算出的高度不合理:', calculatedHeight)
				}
			} else {
				console.log('❌ 无法获取视频尺寸，保持当前高度:', this.videoHeight)
			}
		},

		// 预加载视频信息
		preloadVideoInfo() {
			// 不再设置默认高度，让视频事件来处理
			// 只是确保视频组件能正确触发事件
			console.log('视频组件已准备就绪，等待视频事件触发高度计算')
		},

		// 视频错误处理
		onVideoError(event) {
			console.error('视频加载错误:', event)
			// 视频加载失败时，使用一个合理的默认高度
			const systemInfo = uni.getSystemInfoSync()
			const containerWidth = systemInfo.windowWidth - 40
			this.videoHeight = Math.round(containerWidth * 0.6) // 使用3:5的比例作为默认
			console.log('视频加载失败，使用默认高度:', this.videoHeight)
		},

		// 图片加载完成事件
		onImageLoaded(event) {
			console.log('图片加载完成:', event)
			// 这里可以根据图片的实际尺寸调整容器高度
			// 但由于uni-app的限制，我们保持固定高度以确保显示正常
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	width: 100%;
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	padding-bottom: 0; /* 移除底部导航栏空间 */
}



/* 主要内容区域 */
.main-content {
	flex: 1;
	padding: 5px 5px 100px; /* 增加底部padding，为悬浮按钮留出空间 */
	display: flex;
	flex-direction: column;
	overflow-y: auto;
	gap: 3px; /* 缩小整体间距 */
	align-items: center; /* 让内容居中 */
	justify-content: flex-start;
}

/* 加载状态 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 20px;
	gap: 16px;
}

.loading-spinner {
	width: 32px;
	height: 32px;
	border: 3px solid #f3f3f3;
	border-top: 3px solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 14px;
	color: #666;
}

/* 错误状态 */
.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 20px;
	gap: 16px;
}

.error-text {
	font-size: 14px;
	color: #ff4757;
	text-align: center;
}

.retry-btn {
	background-color: #007AFF;
	color: white;
	padding: 8px 16px;
	border-radius: 16px;
	font-size: 14px;
}

/* 欢迎容器 */
.welcome-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 12px;
	padding: 20px;
	width: 100%;
}

/* AI助手卡片 */
.ai-card {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 3px; /* 缩小头像与标题间距 */
	width: 100%;
	margin: 0 auto; /* 确保居中 */
	/* 移除背景、圆角和阴影，让头像单独显示 */

	.ai-avatar-container {
		width: 80px;
		height: 80px;
		border-radius: 20px;
		overflow: hidden;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

		.ai-avatar {
			width: 100%;
			height: 100%;
		}
	}

	/* 图片卡片样式 - 继承 media-card 的样式 */

	.image-container {
		position: relative;
		width: 100%;
		height: 200px;
		overflow: hidden;
		border-radius: 10px;
	}

	.media-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
		background: #f5f5f5;
	}

/* 确保视频也使用相同样式 */
video.media-image {
	width: 100%;
	height: 100%;
	object-fit: contain;
	background: #000;
	border-radius: 8px;
}



}

/* 欢迎消息 */
.welcome-message {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 3px; /* 减少上下间距 */
	text-align: center;
	width: 100%; /* 完全占满宽度 */
	max-width: 350px; /* 与媒体卡片保持一致的最大宽度 */
	margin: 3px auto; /* 减少上下外边距 */
	padding: 0; /* 无内边距 */
	box-sizing: border-box;

	.welcome-title {
		font-size: 16px;
		font-weight: 500;
		color: #000;
		line-height: 20px;
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		border: 1px solid #dee2e6;
		border-radius: 10px;
		padding: 10px 16px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
		text-align: center;
		display: block; /* 改为block确保居中 */
		width: auto; /* 根据内容自适应宽度 */
		margin: 0 auto; /* 确保居中显示 */
		box-sizing: border-box;
		min-height: auto; /* 高度自适应文字内容 */
	}

	.welcome-desc {
		font-size: 15px;
		color: #666;
		line-height: 22px;
		font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', 'Arial', sans-serif;
		font-style: italic;
		text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
		letter-spacing: 0.5px;
		background: rgba(255, 255, 255, 0.8);
		border: 1px solid rgba(0, 0, 0, 0.08);
		border-radius: 10px;
		padding: 10px 16px;
		box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
		text-align: center;
		display: block; /* 改为block，占满容器宽度 */
		width: 100%; /* 占满容器宽度，与媒体卡片一致 */
		box-sizing: border-box;
		backdrop-filter: blur(10px);
		min-height: auto; /* 高度自适应文字内容 */
	}

	.user-type {
		font-size: 14px;
		color: #999;
		margin-top: 8px;
	}
}

/* 聊天消息列表 */
.chat-messages {
	width: 100%;
	padding: 20px 0;
	display: flex;
	flex-direction: column;
	gap: 16px;
	margin-top: 10px;
}

.message-item {
	display: flex;
	gap: 12px;

	&.user-message {
		flex-direction: row-reverse;

		.message-content {
			align-items: flex-end;
		}

		.message-bubble {
			background-color: #007AFF;
			color: white;
		}
	}

	&.assistant-message {
		flex-direction: row;

		.message-content {
			align-items: flex-start;
		}

		.message-bubble {
			background-color: #f5f5f5;
			color: #333;
		}
	}

	&.error-message {
		.message-bubble {
			background-color: #ffe6e6;
			color: #ff4757;
			border: 1px solid #ffcccc;
		}
	}
}

.message-avatar {
	width: 36px;
	height: 36px;
	flex-shrink: 0;

	.avatar {
		width: 100%;
		height: 100%;
		border-radius: 18px;
	}
}

.message-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
	max-width: calc(100% - 60px);
}

.message-bubble {
	padding: 12px 16px;
	border-radius: 18px;
	max-width: 100%;
	word-wrap: break-word;

	&.typing {
		padding: 16px;
		background-color: #f5f5f5;
	}
}

.message-text {
	font-size: 16px;
	line-height: 22px;
	word-wrap: break-word;
	white-space: pre-wrap;
}

/* 图片消息样式 */
.image-message {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.message-image {
	max-width: 100%;
	max-height: 300px;
	border-radius: 8px;
	cursor: pointer;
}

.image-actions {
	display: flex;
	justify-content: flex-end;
}

/* URL消息样式 */
.url-message {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.url-text {
	font-size: 14px;
	color: #007AFF;
	word-wrap: break-word;
	cursor: pointer;
	text-decoration: underline;
}

.url-actions {
	display: flex;
	justify-content: flex-end;
}

/* 复制按钮样式 */
.copy-btn {
	font-size: 12px;
	color: #007AFF;
	padding: 4px 8px;
	border: 1px solid #007AFF;
	border-radius: 4px;
	cursor: pointer;
	background: transparent;
}

.copy-btn:hover {
	background: #007AFF;
	color: white;
}

/* 正在工作中的消息样式 */
.working-message {
	opacity: 0.9;
}

.working-bubble {
	background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
	border: 1px solid #b3d9ff;
}

.working-indicator {
	display: flex;
	align-items: center;
	gap: 8px;
}

.loading-dots {
	display: flex;
	gap: 4px;
}

.dot {
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background: #007AFF;
	animation: loading-bounce 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) {
	animation-delay: -0.32s;
}

.dot:nth-child(2) {
	animation-delay: -0.16s;
}

.working-text {
	font-size: 14px;
	color: #666;
	font-style: italic;
}

@keyframes loading-bounce {
	0%, 80%, 100% {
		transform: scale(0.8);
		opacity: 0.5;
	}
	40% {
		transform: scale(1);
		opacity: 1;
	}
}



.stop-text {
	font-size: 14px;
	font-weight: 500;
}

/* 开始聊天按钮样式 */
.start-chat-container {
	position: fixed;
	bottom: 20px;
	left: 50%;
	transform: translateX(-50%);
	z-index: 999;
	display: flex;
	align-items: center;
	gap: 24px;
	width: auto;
	justify-content: center;
}

/* 功能按钮样式 */
.function-btn {
	width: 50px;
	height: 50px;
	border-radius: 25px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.history-btn {
	background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
}

.share-btn {
	background: linear-gradient(135deg, #4ECDC4 0%, #6BCCC4 100%);
}

.function-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.btn-icon {
	font-size: 20px;
	color: white;
}

.start-chat-btn {
	display: flex;
	align-items: center;
	gap: 12px;
	background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
	color: white;
	padding: 16px 36px;
	border-radius: 30px;
	box-shadow: 0 8px 20px rgba(0, 122, 255, 0.3);
	cursor: pointer;
	transition: all 0.3s ease;
	min-width: 120px;
	white-space: nowrap;
	justify-content: center;
}

.start-chat-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 12px 25px rgba(0, 122, 255, 0.4);
}

.chat-icon {
	font-size: 20px;
}

.chat-text {
	font-size: 16px;
	font-weight: 600;
}

/* 聊天弹窗样式 */
.chat-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
	padding: 20px;
}

.chat-modal {
	background: white;
	border-radius: 20px;
	width: 90%;
	max-width: 500px;
	max-height: 85vh;
	height: 600px;
	display: flex;
	flex-direction: column;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
	overflow: hidden;
	position: relative;
	z-index: 1001;
}

/* 弹窗头部 */
.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20px;
	background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
	color: white;
}

.header-info {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 8px;
}

.modal-title {
	font-size: 18px;
	font-weight: 600;
	color: white;
}

.points-info {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.points-text {
	font-size: 14px;
	color: rgba(255, 255, 255, 0.9);
	font-weight: 500;
}

.consumption-text {
	font-size: 14px;
	color: rgba(255, 255, 255, 0.9);
	font-weight: 500;
}

.close-btn {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.close-btn:hover {
	background: rgba(255, 255, 255, 0.3);
}

.close-icon {
	font-size: 16px;
	font-weight: bold;
}



/* 弹窗聊天区域 */
.modal-chat-container {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	position: relative;
	z-index: 1;
}

.welcome-message {
	display: flex;
	align-items: flex-start;
	gap: 12px;
	padding: 20px;
	border-bottom: 1px solid #f0f0f0;
}

.welcome-avatar {
	flex-shrink: 0;
}

.welcome-avatar .avatar {
	width: 36px;
	height: 36px;
	border-radius: 50%;
}

.welcome-content {
	flex: 1;
}

.welcome-text {
	font-size: 14px;
	color: #666;
	line-height: 1.5;
}

.modal-messages-container {
	flex: 1;
	overflow-y: auto;
	padding: 15px 20px;
	max-height: 500px;
	min-height: 400px;
}

.modal-message-item {
	display: flex;
	margin-bottom: 16px;
	align-items: flex-start;
	gap: 12px;
}

.modal-message-item.user-message {
	flex-direction: row-reverse;
}

.modal-message-avatar {
	flex-shrink: 0;
}

.modal-message-avatar .avatar {
	width: 32px;
	height: 32px;
	border-radius: 50%;
}

.modal-message-content {
	flex: 1;
	max-width: 70%;
	display: flex;
	flex-direction: column;
}

.modal-message-bubble {
	padding: 12px 16px;
	border-radius: 18px;
	word-wrap: break-word;
	position: relative;
	display: inline-block;
	max-width: 100%;
	width: fit-content;
	min-width: 60px;
}

.modal-message-item.user-message .modal-message-content {
	align-items: flex-end;
}

.modal-message-item.user-message .modal-message-bubble {
	background: #007AFF;
	color: white;
}

.modal-message-item.assistant-message .modal-message-content {
	align-items: flex-start;
}

.modal-message-item.assistant-message .modal-message-bubble {
	background: #f0f0f0;
	color: #333;
}

.modal-message-text {
	font-size: 14px;
	line-height: 1.4;
}



/* 弹窗输入区域 */
.modal-input-container {
	border-top: 1px solid #f0f0f0;
	padding: 16px 20px;
	background: #fafafa;
}

.modal-input-wrapper {
	display: flex;
	align-items: center;
	gap: 8px;
	background: white;
	border-radius: 25px;
	padding: 8px 16px;
	border: 1px solid #e0e0e0;
}

.modal-add-btn {
	width: 32px;
	height: 32px;
	border-radius: 16px;
	background: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	flex-shrink: 0;
}

.modal-add-btn:hover {
	background: #e0e0e0;
}

.add-icon {
	font-size: 18px;
	color: #666;
	font-weight: bold;
}

/* 附件选择菜单 */
.attachment-menu-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.3);
	display: flex;
	align-items: flex-end;
	justify-content: center;
	z-index: 10000;
	padding: 20px;
}

.attachment-menu {
	background: white;
	border-radius: 15px 15px 0 0;
	width: 100%;
	max-width: 500px;
	padding: 20px 0;
	animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

.attachment-option {
	display: flex;
	align-items: center;
	padding: 15px 20px;
	cursor: pointer;
	transition: background-color 0.3s ease;
	gap: 15px;
}

.attachment-option:hover {
	background: #f5f5f5;
}

.attachment-option.cancel-option {
	border-top: 1px solid #e0e0e0;
	margin-top: 10px;
	justify-content: center;
	color: #666;
}

.option-icon {
	font-size: 24px;
	width: 30px;
	text-align: center;
}

.option-text {
	font-size: 16px;
	color: #333;
}

.cancel-option .option-text {
	color: #666;
}

.modal-message-input {
	flex: 1;
	border: none;
	outline: none;
	font-size: 14px;
	padding: 8px 0;
}

.modal-send-btn {
	background: #007AFF;
	color: white;
	padding: 8px 16px;
	border-radius: 18px;
	font-size: 14px;
	cursor: pointer;
	transition: all 0.3s ease;
	white-space: nowrap;
}

.modal-send-btn.disabled {
	background: #ccc;
	cursor: not-allowed;
}

.modal-send-btn:not(.disabled):hover {
	background: #0056b3;
}

.modal-stop-btn {
	background: #ff4757;
	color: white;
	padding: 8px 12px;
	border-radius: 18px;
	font-size: 14px;
	cursor: pointer;
	transition: all 0.3s ease;
	white-space: nowrap;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-stop-btn:hover {
	background: #ff3742;
}

.modal-stop-btn .stop-icon {
	font-size: 16px;
	font-weight: bold;
}

.message-time {
	font-size: 12px;
	color: #999;
	margin: 0 8px;
}

/* 打字指示器 */
.typing-indicator {
	display: flex;
	gap: 4px;
	align-items: center;
}

.typing-dot {
	width: 6px;
	height: 6px;
	background-color: #999;
	border-radius: 50%;
	animation: typing 1.4s infinite ease-in-out;

	&:nth-child(1) {
		animation-delay: -0.32s;
	}

	&:nth-child(2) {
		animation-delay: -0.16s;
	}
}

@keyframes typing {
	0%, 80%, 100% {
		transform: scale(0.8);
		opacity: 0.5;
	}
	40% {
		transform: scale(1);
		opacity: 1;
	}
}



/* 底部输入区域 */
.input-area {
	position: fixed;
	bottom: 20px;
	left: 20px;
	right: 20px;
	z-index: 100;

	.input-container {
		display: flex;
		align-items: center;

		.input-wrapper {
			flex: 1;
			background-color: #f8f8f8;
			border-radius: 20px;
			border: none !important;
			outline: none !important;
			box-shadow: none !important;
			display: flex;
			align-items: center;
			padding: 0 4px;
			height: 36px;
		}

			.add-btn {
				width: 28px;
				height: 28px;
				display: flex;
				align-items: center;
				justify-content: center;
				border: none !important;
				outline: none !important;
				box-shadow: none !important;

				.add-icon {
					font-size: 20px;
					color: #666;
				}
			}

			.message-input {
				flex: 1;
				border: none !important;
				border-width: 0 !important;
				border-style: none !important;
				border-color: transparent !important;
				outline: none !important;
				outline-width: 0 !important;
				outline-style: none !important;
				outline-color: transparent !important;
				background: transparent;
				font-size: 16px;
				padding: 0 8px;
				height: 100%;
				box-shadow: none !important;
				-webkit-box-shadow: none !important;
				-moz-box-shadow: none !important;
				-webkit-appearance: none !important;
				-moz-appearance: none !important;
				appearance: none !important;
				-webkit-tap-highlight-color: transparent !important;
				-webkit-focus-ring-color: transparent !important;
			}

			.message-input:focus {
				border: none !important;
				border-width: 0 !important;
				outline: none !important;
				outline-width: 0 !important;
				box-shadow: none !important;
				-webkit-box-shadow: none !important;
				-moz-box-shadow: none !important;
			}

			.message-input:active {
				border: none !important;
				outline: none !important;
				box-shadow: none !important;
			}

			.message-input::before,
			.message-input::after {
				display: none !important;
			}

			.send-btn {
				min-width: 60px;
				height: 28px;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #f0f0f0;
				border-radius: 14px;
				border: none !important;
				outline: none !important;
				box-shadow: none !important;
				transition: all 0.3s ease;

				&.active {
					background-color: #007AFF;
					color: white;
				}

				.send-icon {
					font-size: 14px;
					font-weight: 500;
				}

				.sending-icon {
					display: flex;
					align-items: center;
					justify-content: center;

					.spinner {
						width: 16px;
						height: 16px;
						border: 2px solid #f3f3f3;
						border-top: 2px solid #007AFF;
						border-radius: 50%;
						animation: spin 1s linear infinite;
					}
				}
			}
		}
	}

/* 多媒体内容卡片 */
.media-cards {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 3px; /* 进一步减少卡片之间的间距 */
	margin: 0 auto; /* 确保居中 */
	width: 100%; /* 完全占满宽度 */
	max-width: 350px; /* 与其他卡片保持一致 */
	padding: 0; /* 无内边距 */
	box-sizing: border-box;
}

.media-card {
	background: rgba(255, 255, 255, 0.9) !important;
	border-radius: 12px !important;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
	border: 3px solid #c0c0c0 !important;
	overflow: hidden !important;
	transition: all 0.3s ease;
	width: 100% !important;
	max-width: 350px !important; /* 减小最大宽度，保持与其他卡片一致 */
	position: relative;
	padding: 12px !important;
	margin: 0 auto !important;
}

/* 更具体的选择器确保样式应用 */
.media-cards .media-section .media-card {
	background: rgba(255, 255, 255, 0.9) !important;
	border: 3px solid #c0c0c0 !important;
	border-radius: 12px !important;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
	padding: 12px !important;
}

.media-cards .media-section .video-card {
	border: 3px solid #c0c0c0 !important;
}

.media-cards .media-section .image-card {
	border: 3px solid #c0c0c0 !important;
}

.media-cards .media-section .text-card {
	border: 3px solid #c0c0c0 !important;
}





.media-card:active {
	transform: scale(0.98);
	box-shadow: 0 1px 8px rgba(0, 0, 0, 0.12);
}

/* 媒体区域样式 */
.media-section {
	margin-bottom: 0;
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.media-header {
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	flex-direction: row !important;
	padding: 10px 16px;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-bottom: 1px solid #f0f0f0;
	gap: 3px;
	margin-bottom: 3px;
	border-radius: 12px;
}

.media-type-icon {
	width: 24px;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.type-icon {
	font-size: 16px;
}

.media-title {
	font-size: 15px;
	font-weight: 500;
	color: #333;
}

/* 视频卡片样式 */
.video-card .media-header {
	margin-bottom: 3px;
}

.image-card .media-header {
	margin-bottom: 3px;
}

.text-card .media-header {
	margin-bottom: 3px;
}

/* 图片和视频容器样式 */
.image-container {
	position: relative;
	width: 100%;
	height: auto;
	overflow: hidden;
	border-radius: 10px;
	background: #f5f5f5;
}

.media-image {
	width: 100%;
	height: auto;
	border-radius: 8px;
	display: block;
}

/* 文字卡片样式 */
.text-card {
	min-height: 120px;
	display: flex;
	flex-direction: column;
	/* 其他样式继承 media-card */
}

/* 文字内容容器 */
.text-content-wrapper {
	position: relative;
	width: 100%;
	min-height: auto;
	padding: 0;
	background: rgba(248, 249, 250, 0.8);
	border-radius: 10px;
	margin-top: 3px;
	overflow: visible;
}

/* 通用弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 20px 0 20px;
	border-bottom: 1px solid #f0f0f0;
	margin-bottom: 20px;
}

.modal-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.close-btn {
	width: 30px;
	height: 30px;
	border-radius: 15px;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 16px;
	color: #666;
	transition: all 0.3s ease;
}

.close-btn:hover {
	background: #e0e0e0;
}

/* 历史记录弹窗样式 */
.history-modal {
	width: 90%;
	max-width: 500px;
	max-height: 80vh;
	background: white;
	border-radius: 12px;
	overflow: hidden;
}

.history-content {
	max-height: 60vh;
	overflow-y: auto;
	padding: 0 20px 20px 20px;
}

.loading-container, .empty-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40px 20px;
}

.loading-text, .empty-text {
	color: #999;
	font-size: 14px;
}

.history-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.history-item {
	padding: 16px;
	background: #f8f9fa;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.history-item:hover {
	background: #e9ecef;
	transform: translateY(-1px);
}

.history-time {
	font-size: 12px;
	color: #999;
	margin-bottom: 8px;
}

.history-preview {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

/* 工作流历史记录样式 */
.workflow-title {
	font-size: 14px;
	font-weight: 600;
	color: #333;
	line-height: 1.4;
}

.workflow-status {
	font-size: 12px;
	color: #666;
	margin-top: 2px;
}

.workflow-preview {
	font-size: 12px;
	color: #888;
	margin-top: 4px;
	line-height: 1.3;
}

.workflow-consumption {
	font-size: 11px;
	color: #999;
	margin-top: 4px;
}

/* 历史记录输入输出样式 */
.input-section, .output-section {
	margin-top: 8px;
}

.section-title {
	font-size: 12px;
	color: #666;
	font-weight: 600;
	display: block;
	margin-bottom: 4px;
}

.input-content {
	font-size: 12px;
	color: #333;
	line-height: 1.4;
	display: block;
}

.output-links {
	display: flex;
	flex-direction: column;
	gap: 6px;
}

.output-link-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 4px 8px;
	background: #f8f9fa;
	border-radius: 4px;
	border: 1px solid #e9ecef;
	margin-bottom: 3px;
	min-height: 28px;
}

.output-link-item .link-url {
	flex: 1;
	font-size: 11px;
	color: #495057;
	line-height: 1.2;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-right: 8px;
}

.copy-link-btn-small {
	padding: 4px 6px;
	background: #007bff;
	color: white;
	border: none;
	border-radius: 3px;
	font-size: 10px;
	cursor: pointer;
	transition: background 0.3s ease;
	min-width: auto;
	display: flex;
	align-items: center;
	justify-content: center;
}

.copy-link-btn-small:hover {
	background: #0056b3;
}

.copy-link-btn-small .copy-icon {
	font-size: 10px;
}

.copy-link-btn-text {
	background: #007bff;
	color: white;
	border: none;
	border-radius: 4px;
	padding: 4px 8px;
	font-size: 10px;
	cursor: pointer;
	white-space: nowrap;
	flex-shrink: 0;
	transition: background-color 0.2s;
}

.copy-link-btn-text:hover {
	background: #0056b3;
}

.copy-link-btn-text.copied {
	background: #28a745;
	color: white;
}

.copy-link-btn-text.copied:hover {
	background: #218838;
}

/* 兼容旧的聊天记录样式 */
.user-message, .ai-message {
	font-size: 14px;
	line-height: 1.4;
}

.user-message {
	color: #333;
}

.ai-message {
	color: #666;
}

.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 16px;
	cursor: pointer;
	color: #007AFF;
	font-size: 14px;
}

.load-more:hover {
	background: #f8f9fa;
	border-radius: 8px;
}

/* 分享弹窗样式 */
.share-modal {
	width: 90%;
	max-width: 400px;
	background: white;
	border-radius: 12px;
	overflow: hidden;
}

.share-content {
	padding: 0 20px 20px 20px;
}

.share-info {
	margin-bottom: 24px;
}

.agent-info {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 16px;
	background: #f8f9fa;
	border-radius: 8px;
}

.agent-avatar {
	width: 50px;
	height: 50px;
	border-radius: 25px;
	background: #e0e0e0;
}

.agent-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.agent-name {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.agent-desc {
	font-size: 14px;
	color: #666;
	line-height: 1.4;
}

.share-methods {
	display: flex;
	flex-direction: column;
	gap: 12px;
	margin-bottom: 20px;
}

.share-method {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 16px;
	background: #f8f9fa;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.share-method:hover {
	background: #e9ecef;
	transform: translateY(-1px);
}

.method-icon {
	font-size: 20px;
}

.method-text {
	font-size: 16px;
	color: #333;
}

.share-code-container {
	padding: 16px;
	background: #f8f9fa;
	border-radius: 8px;
	border: 1px dashed #ddd;
}

.share-code-label {
	font-size: 14px;
	color: #666;
	margin-bottom: 8px;
}

.share-code-box {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 12px;
}

.share-code {
	flex: 1;
	font-size: 16px;
	font-weight: 600;
	color: #007AFF;
	font-family: monospace;
}

.copy-code-btn {
	padding: 6px 12px;
	background: #007AFF;
	color: white;
	border-radius: 4px;
	font-size: 12px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.copy-code-btn:hover {
	background: #0056CC;
}

/* 文字教学内容 - 清晰易读的字体样式 */
.text-teaching-content {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', sans-serif;
	font-size: 14px;
	color: #333;
	line-height: 1.6;
	font-weight: 400;
	word-wrap: break-word;
	display: block;
	width: 100%;
	min-height: auto;
	padding: 16px;
	box-sizing: border-box;
	background: rgba(255, 255, 255, 0.8);
	border: 1px solid rgba(0, 0, 0, 0.08);
	border-radius: 10px;
	box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
}

.media-description {
	padding: 8px 16px 12px;
	border-top: 1px solid #f8f8f8;
}

.description-text {
	font-size: 12px;
	color: #999;
	line-height: 16px;
	font-style: italic;
}

/* ========== 工作流相关样式 ========== */

/* 工作流信息展示 */
.workflow-info {
	display: flex;
	align-items: center;
	padding: 20px;
	border-bottom: 1px solid #f0f0f0;
}

.workflow-avatar {
	width: 50px;
	height: 50px;
	border-radius: 25px;
	overflow: hidden;
	margin-right: 15px;

	.avatar {
		width: 100%;
		height: 100%;
	}
}

.workflow-content {
	flex: 1;
}

.workflow-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
	margin-bottom: 5px;
}

.workflow-desc {
	font-size: 14px;
	color: #666;
	line-height: 1.4;
}

/* 工作流参数输入表单 */
.workflow-params-container {
	flex: 1;
	padding: 20px;
	overflow-y: auto;
	position: relative;
	z-index: 1;
}

.params-form {
	margin-bottom: 20px;
	position: relative;
	z-index: 1;
}

.param-item {
	margin-bottom: 20px;
}

.param-label {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
}

.label-text {
	font-size: 14px;
	font-weight: 500;
	color: #333;
}

.required-mark {
	color: #ff4757;
	margin-left: 4px;
	font-size: 14px;
}

.param-input {
	width: 100%;
	position: relative;
	z-index: 1;
}

.param-description {
	margin-bottom: 8px;
}

.description-text {
	font-size: 12px;
	color: #999;
	line-height: 1.4;
}

/* 文本输入框 */
.text-input, .textarea-input {
	width: 100%;
	padding: 12px 16px;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	font-size: 14px;
	color: #333;
	background-color: #fff;
	box-sizing: border-box;
	position: relative;
	z-index: 10;
	pointer-events: auto;
	-webkit-user-select: text;
	user-select: text;
}

.text-input:focus, .textarea-input:focus {
	border-color: #007aff;
	outline: none;
	z-index: 11;
}

.textarea-input {
	min-height: 80px;
	resize: vertical;
}

/* 移动端输入框优化 */
@media (max-width: 768px) {
	.text-input, .textarea-input {
		-webkit-appearance: none;
		appearance: none;
		-webkit-tap-highlight-color: transparent;
		touch-action: manipulation;
	}
}

/* 测试按钮样式 */
.test-input-wrapper {
	margin-bottom: 10px;
}

.test-btn {
	padding: 8px 16px;
	background-color: #007aff;
	color: white;
	border: none;
	border-radius: 4px;
	font-size: 12px;
	cursor: pointer;
	margin-right: 10px;
}

.debug-text {
	font-size: 12px;
	color: #666;
	background-color: #f5f5f5;
	padding: 4px 8px;
	border-radius: 4px;
}

/* 工作流参数输入框样式优化 */
.param-input .text-input {
	width: 100%;
	height: 40px;
	border: 1px solid #ddd;
	border-radius: 6px;
	padding: 0 12px;
	font-size: 14px;
	background-color: #fff;
	box-sizing: border-box;
	outline: none;
	transition: border-color 0.2s ease;
}

.param-input .text-input:focus {
	border-color: #007aff;
	box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
}

/* 选择器 */
.select-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px 16px;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	background-color: #fff;
	font-size: 14px;
	color: #333;
}

.select-arrow {
	color: #999;
	font-size: 12px;
}

/* 精致气泡按钮组样式 - 强制优先级 */
.bubble-button-group {
	display: flex !important;
	flex-wrap: wrap !important;
	gap: 8px !important;
	margin-top: 10px !important;
	padding: 4px 0 !important;
}

.bubble-button {
	padding: 6px 14px !important;
	border: 1px solid #e0e0e0 !important;
	border-radius: 16px !important;
	background-color: #ffffff !important;
	cursor: pointer !important;
	transition: all 0.25s ease !important;
	min-width: 50px !important;
	text-align: center !important;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08) !important;
	position: relative !important;
	font-size: 13px !important;
	font-weight: 400 !important;
}

.bubble-button:hover {
	border-color: #007aff !important;
	background-color: #f6f9ff !important;
	transform: translateY(-0.5px) !important;
	box-shadow: 0 2px 6px rgba(0, 122, 255, 0.12) !important;
}

.bubble-button-selected {
	border-color: #007aff !important;
	background-color: #007aff !important;
	color: white !important;
	transform: translateY(-0.5px) !important;
	box-shadow: 0 3px 8px rgba(0, 122, 255, 0.25) !important;
}

.bubble-button-text {
	font-size: 13px !important;
	font-weight: 400 !important;
	color: inherit !important;
	line-height: 1.2 !important;
}

/* 多选按钮组 */
.checkbox-button-group {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}

.checkbox-button {
	padding: 8px 16px;
	border: 1px solid #ddd;
	border-radius: 20px;
	background-color: #fff;
	cursor: pointer;
	transition: all 0.2s ease;
	min-width: 60px;
	text-align: center;
}

.checkbox-button:hover {
	border-color: #007aff;
}

.checkbox-button-active {
	background-color: #007aff;
	border-color: #007aff;
}

.checkbox-button-text {
	font-size: 14px;
	color: #333;
}

.checkbox-button-active .checkbox-button-text {
	color: #fff;
}

/* 文件上传组件 */
.upload-container {
	width: 100%;
	min-height: 120px;
	border: 2px dashed #e0e0e0;
	border-radius: 8px;
	position: relative;
	overflow: hidden;
}

.upload-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 120px;
	background-color: #fafafa;
	cursor: pointer;
	transition: all 0.3s ease;
}

.upload-btn:hover {
	background-color: #f0f0f0;
	border-color: #007aff;
}

.upload-icon {
	font-size: 32px;
	margin-bottom: 8px;
}

.upload-text {
	font-size: 14px;
	color: #666;
}

/* 上传预览 */
.upload-preview {
	position: relative;
	width: 100%;
	height: 120px;
}

.preview-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.preview-video {
	width: 100%;
	height: 100%;
}

.remove-btn {
	position: absolute;
	top: 8px;
	right: 8px;
	width: 24px;
	height: 24px;
	background-color: rgba(0, 0, 0, 0.6);
	color: #fff;
	border-radius: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16px;
	cursor: pointer;
	z-index: 10;
}

.remove-btn:hover {
	background-color: rgba(0, 0, 0, 0.8);
}

/* 工作流执行结果 */
.workflow-result {
	margin-top: 20px;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	overflow: hidden;
}

.result-header {
	padding: 12px 16px;
	background-color: #f8f9fa;
	border-bottom: 1px solid #e0e0e0;
}

.result-title {
	font-size: 14px;
	font-weight: 600;
	color: #333;
}

.result-content {
	padding: 16px;
	background-color: #fff;
}

.result-text {
	font-size: 14px;
	color: #333;
	line-height: 1.6;
	white-space: pre-wrap;
	word-break: break-word;
}

/* 媒体容器样式 */
.media-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16px;
}

/* 图片包装器 */
.image-wrapper {
	width: 100%;
	position: relative;
}

/* 图片容器 */
.image-container {
	position: relative;
	width: 100%;
	border-radius: 8px;
	overflow: hidden;
	border: 1px solid #e0e0e0;
	background: #f8f9fa;
}

.result-image {
	width: 100%;
	max-height: 400px;
	border-radius: 8px;
	object-fit: contain;
	display: block;
	cursor: pointer;
	transition: transform 0.3s ease;
}

.result-image:hover {
	transform: scale(1.02);
}

/* 图片覆盖层 */
.image-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: opacity 0.3s ease;
	cursor: pointer;
	border-radius: 8px;
}

.image-container:hover .image-overlay {
	opacity: 1;
}

.preview-icon {
	font-size: 32px;
	color: white;
	margin-bottom: 8px;
}

.preview-text {
	color: white;
	font-size: 14px;
	font-weight: 500;
}

/* 图片加载状态 */
.image-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 20px;
	background: #f8f9fa;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	min-height: 200px;
}

.loading-text {
	font-size: 16px;
	color: #6c757d;
	margin-top: 12px;
}

/* 图片错误状态 */
.image-error {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 20px;
	background: #fff5f5;
	border: 1px solid #fed7d7;
	border-radius: 8px;
	min-height: 200px;
	gap: 12px;
}

.error-icon {
	font-size: 32px;
}

.error-text {
	font-size: 16px;
	color: #e53e3e;
	font-weight: 500;
}

.error-url {
	font-size: 12px;
	color: #a0aec0;
	word-break: break-all;
	text-align: center;
	max-width: 100%;
}

.retry-btn {
	padding: 8px 16px;
	background: #e53e3e;
	color: white;
	border: none;
	border-radius: 6px;
	font-size: 14px;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.retry-btn:hover {
	background: #c53030;
}

.result-video {
	width: 100%;
	max-height: 400px;
	border-radius: 8px;
	border: 1px solid #e0e0e0;
}

.link-container {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.link-preview {
	padding: 16px;
	background: #f8f9fa;
	border-radius: 8px;
	border: 1px solid #e0e0e0;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.link-preview:hover {
	background: #e9ecef;
}

.link-text {
	font-size: 14px;
	color: #007bff;
	word-break: break-all;
	text-decoration: underline;
}

.text-container {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.media-actions {
	display: flex;
	gap: 12px;
	justify-content: center;
	flex-wrap: wrap;
}

.action-btn {
	padding: 8px 16px;
	border-radius: 6px;
	font-size: 13px;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
	min-width: 80px;
	color: white;
}

.save-btn {
	background: #28a745;
}

.save-btn:hover {
	background: #218838;
}

.copy-btn {
	background: #6c757d;
}

.copy-btn:hover {
	background: #5a6268;
}

.open-btn {
	background: #007bff;
}

.open-btn:hover {
	background: #0056b3;
}

/* 链接列表样式 */
.links-container {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.link-item {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px;
	background: #f8f9fa;
	border-radius: 8px;
	border: 1px solid #e9ecef;
}

.link-content {
	flex: 1;
	min-width: 0; /* 允许内容收缩 */
}

.link-url {
	font-size: 14px;
	color: #495057;
	line-height: 1.4;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	display: block;
}

.copy-link-btn {
	display: flex;
	align-items: center;
	gap: 4px;
	padding: 8px 12px;
	background: #007bff;
	color: white;
	border: none;
	border-radius: 6px;
	font-size: 12px;
	cursor: pointer;
	transition: background 0.3s ease;
	flex-shrink: 0; /* 防止按钮被压缩 */
	white-space: nowrap;
}

.copy-link-btn:hover {
	background: #0056b3;
}

.copy-icon {
	font-size: 12px;
}

.copy-text {
	font-size: 12px;
}

.preview-btn {
	background: #17a2b8;
}

.preview-btn:hover {
	background: #138496;
}

/* 工作流执行中状态 */
.workflow-executing {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 30px 20px;
}

.executing-indicator {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.executing-text {
	font-size: 14px;
	color: #666;
	margin-top: 10px;
}

/* 工作流执行按钮 */
.workflow-action-wrapper {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 20px;
}

.execute-workflow-btn {
	flex: 1;
	height: 44px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 22px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 16px;
	font-weight: 600;
	transition: all 0.3s ease;
}

.execute-workflow-btn:active {
	transform: scale(0.98);
}

.execute-workflow-btn.disabled {
	background: #ccc;
	color: #999;
}

.modal-stop-btn {
	flex: 1;
	height: 44px;
	background-color: #ff4757;
	border-radius: 22px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 16px;
	font-weight: 600;
}

.stop-text {
	margin-left: 8px;
	font-size: 14px;
}

/* 自定义点数不足弹窗样式 */
.points-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	padding: 20px;
}

.points-modal {
	background: white;
	border-radius: 20px;
	width: 90%;
	max-width: 400px;
	overflow: hidden;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
	animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
	from {
		opacity: 0;
		transform: scale(0.8) translateY(-20px);
	}
	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

.points-modal-header {
	background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
	color: white;
	padding: 20px;
	display: flex;
	align-items: center;
	position: relative;
}

.points-icon {
	font-size: 24px;
	margin-right: 12px;
}

.points-title {
	font-size: 20px;
	font-weight: bold;
	flex: 1;
}

.points-close-btn {
	width: 32px;
	height: 32px;
	border-radius: 16px;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.2s ease;
}

.points-close-btn:hover {
	background: rgba(255, 255, 255, 0.3);
}

.points-close-icon {
	font-size: 16px;
	font-weight: bold;
}

.points-modal-content {
	padding: 24px;
}

.points-info-card {
	background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
	border-radius: 16px;
	padding: 20px;
	display: flex;
	align-items: center;
	margin-bottom: 20px;
	border: 1px solid #e1e8ff;
}

.points-current,
.points-required {
	flex: 1;
	text-align: center;
}

.points-divider {
	width: 1px;
	height: 40px;
	background: #d1d9ff;
	margin: 0 20px;
}

.points-label {
	font-size: 12px;
	color: #666;
	display: block;
	margin-bottom: 4px;
}

.points-value {
	font-size: 24px;
	font-weight: bold;
	color: #007AFF;
	display: block;
}

.points-value.required {
	color: #ff6b6b;
}

.points-unit {
	font-size: 12px;
	color: #999;
	margin-left: 2px;
}

.points-message {
	text-align: center;
}

.message-text {
	font-size: 16px;
	color: #333;
	line-height: 1.5;
	display: block;
	margin-bottom: 8px;
}

.message-sub {
	font-size: 14px;
	color: #666;
	display: block;
}

.points-modal-actions {
	padding: 0 24px 24px;
	display: flex;
	gap: 12px;
}

.points-btn {
	flex: 1;
	height: 48px;
	border-radius: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.2s ease;
}

.points-btn-cancel {
	background: #f5f5f5;
	color: #666;
}

.points-btn-cancel:hover {
	background: #e8e8e8;
}

.points-btn-primary {
	background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
	color: white;
	box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.points-btn-primary:hover {
	transform: translateY(-1px);
	box-shadow: 0 6px 16px rgba(0, 122, 255, 0.4);
}

.points-btn-text {
	font-size: 16px;
	font-weight: 600;
}

/* 自定义高层级Toast样式 */
.custom-toast-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99999; /* 最高层级，确保在所有弹窗之上 */
	pointer-events: none; /* 不阻止用户操作 */
	display: flex;
	align-items: flex-start;
	justify-content: center;
	padding-top: 100px; /* 距离顶部的距离 */
}

.custom-toast {
	background: rgba(0, 0, 0, 0.8);
	color: white;
	padding: 12px 20px;
	border-radius: 25px;
	display: flex;
	align-items: center;
	gap: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
	animation: toastSlideIn 0.3s ease-out;
	pointer-events: auto; /* 允许点击 */
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
}

.custom-toast.success {
	background: rgba(40, 167, 69, 0.9);
}

.custom-toast.error {
	background: rgba(220, 53, 69, 0.9);
}

.toast-icon {
	font-size: 16px;
}

.toast-text {
	font-size: 14px;
	font-weight: 500;
	color: white;
}

@keyframes toastSlideIn {
	from {
		opacity: 0;
		transform: translateY(-20px) scale(0.9);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}




</style>