/**
 * 会员管理API服务
 */
import { API_BASE_URL } from '@/config/index.js'

// 统一的请求方法
const request = (options) => {
	return new Promise((resolve, reject) => {
		// 获取token
		const token = uni.getStorageSync('token')
		
		uni.request({
			url: `${API_BASE_URL}${options.url}`,
			method: options.method || 'GET',
			data: options.data || {},
			header: {
				'Content-Type': 'application/json',
				'Authorization': token ? `Bearer ${token}` : '',
				...options.header
			},
			success: (res) => {
				if (res.statusCode === 200) {
					resolve(res.data)
				} else {
					// 构造错误对象，包含后端返回的具体错误信息
					const error = new Error(res.data?.message || `请求失败: ${res.statusCode}`)
					error.statusCode = res.statusCode
					error.data = res.data
					reject(error)
				}
			},
			fail: (err) => {
				reject(err)
			}
		})
	})
}

// 会员API服务
export const memberAPI = {
	/**
	 * 获取所有会员列表
	 */
	getAllMembers() {
		return request({
			url: '/api/members',
			method: 'GET'
		})
	},

	/**
	 * 获取会员详情
	 * @param {number} id - 会员ID
	 */
	getMemberById(id) {
		return request({
			url: `/api/members/${id}`,
			method: 'GET'
		})
	},

	/**
	 * 创建会员（注册）
	 * @param {object} memberData - 会员数据
	 */
	createMember(memberData) {
		return request({
			url: '/api/members',
			method: 'POST',
			data: memberData
		})
	},

	/**
	 * 会员注册
	 * @param {object} registerData - 注册数据
	 */
	register(registerData) {
		return request({
			url: '/api/members/register',
			method: 'POST',
			data: registerData
		})
	},

	/**
	 * 更新会员信息
	 * @param {number} id - 会员ID
	 * @param {object} updateData - 更新数据
	 */
	updateMember(id, updateData) {
		return request({
			url: `/api/members/${id}`,
			method: 'PUT',
			data: updateData
		})
	},

	/**
	 * 删除会员
	 * @param {number} id - 会员ID
	 */
	deleteMember(id) {
		return request({
			url: `/api/members/${id}`,
			method: 'DELETE'
		})
	},

	/**
	 * 会员登录
	 * @param {object} loginData - 登录数据
	 */
	login(loginData) {
		return request({
			url: '/api/members/login',
			method: 'POST',
			data: loginData
		})
	},

	/**
	 * 发送验证码
	 * @param {string} contact - 手机号或邮箱
	 */
	sendVerificationCode(contact) {
		return request({
			url: '/api/verification/send',
			method: 'POST',
			data: { contact }
		})
	},

	/**
	 * 验证验证码
	 * @param {string} contact - 手机号或邮箱
	 * @param {string} code - 验证码
	 */
	verifyCode(contact, code) {
		return request({
			url: '/api/verification/verify',
			method: 'POST',
			data: { contact, code }
		})
	},

	/**
	 * 会员登出
	 */
	logout() {
		return request({
			url: '/api/members/logout',
			method: 'POST'
		})
	},

	/**
	 * 获取今日注册会员数量
	 */
	getTodayRegistrations() {
		return request({
			url: '/api/members/stats/today',
			method: 'GET'
		})
	},

	/**
	 * 获取活跃会员数量
	 */
	getActiveMembers() {
		return request({
			url: '/api/members/stats/active',
			method: 'GET'
		})
	},

	/**
	 * 获取合伙人数量
	 */
	getPartners() {
		return request({
			url: '/api/members/stats/partners',
			method: 'GET'
		})
	},

	/**
	 * 搜索会员
	 * @param {string} keyword - 搜索关键词
	 */
	searchMembers(keyword) {
		return request({
			url: '/api/members/search',
			method: 'GET',
			data: { keyword }
		})
	},

	/**
	 * 验证用户名是否可用
	 * @param {string} username - 用户名
	 */
	checkUsername(username) {
		return request({
			url: '/api/members/check-username',
			method: 'GET',
			data: { username }
		})
	},

	/**
	 * 验证邮箱是否可用
	 * @param {string} email - 邮箱
	 */
	checkEmail(email) {
		return request({
			url: '/api/members/check-email',
			method: 'GET',
			data: { email }
		})
	},

	/**
	 * 验证手机号是否可用
	 * @param {string} phone - 手机号
	 */
	checkPhone(phone) {
		return request({
			url: '/api/members/check-phone',
			method: 'GET',
			data: { phone }
		})
	},

	/**
	 * 修改密码
	 * @param {object} passwordData - 密码数据
	 */
	changePassword(passwordData) {
		return request({
			url: '/api/members/change-password',
			method: 'POST',
			data: passwordData
		})
	},

	/**
	 * 重置密码
	 * @param {object} resetData - 重置数据
	 */
	resetPassword(resetData) {
		return request({
			url: '/api/members/reset-password',
			method: 'POST',
			data: resetData
		})
	},

	/**
	 * 设置密码（用于忘记密码）
	 * @param {object} passwordData - 密码数据
	 */
	setPassword(passwordData) {
		return request({
			url: '/api/members/set-test-password',
			method: 'POST',
			data: passwordData
		})
	},

	/**
	 * 获取当前登录用户信息
	 */
	getCurrentUser() {
		return request({
			url: '/api/members/me',
			method: 'GET'
		})
	},

	/**
	 * 更新当前用户信息
	 * @param {object} updateData - 更新数据
	 */
	updateCurrentUser(updateData) {
		return request({
			url: '/api/members/me',
			method: 'PUT',
			data: updateData
		})
	},

	/**
	 * 获取积分历史记录
	 * @param {number} page - 页码
	 * @param {number} limit - 每页数量
	 */
	getPointsHistory(page = 1, limit = 20) {
		return request({
			url: '/api/members/points-history',
			method: 'GET',
			data: { page, limit }
		})
	},

	/**
	 * 获取余额历史记录
	 * @param {number} page - 页码
	 * @param {number} limit - 每页数量
	 */
	getBalanceHistory(page = 1, limit = 20) {
		return request({
			url: '/api/members/balance-history',
			method: 'GET',
			data: { page, limit }
		})
	},

	/**
	 * 获取提现历史记录
	 * @param {number} page - 页码
	 * @param {number} limit - 每页数量
	 */
	getWithdrawHistory(page = 1, limit = 20) {
		return request({
			url: '/api/members/withdraw-history',
			method: 'GET',
			data: { page, limit }
		})
	},

	/**
	 * 更新用户头像
	 * @param {string} avatar - 头像URL
	 */
	updateAvatar(avatar) {
		return request({
			url: '/api/members/avatar',
			method: 'PUT',
			data: { avatar }
		})
	},

	/**
	 * 绑定手机号
	 * @param {object} phoneData - 手机号数据
	 */
	bindPhone(phoneData) {
		return request({
			url: '/api/members/bind-phone',
			method: 'POST',
			data: phoneData
		})
	},

	/**
	 * 绑定邮箱
	 * @param {object} emailData - 邮箱数据
	 */
	bindEmail(emailData) {
		return request({
			url: '/api/members/bind-email',
			method: 'POST',
			data: emailData
		})
	},

	/**
	 * 发送验证码
	 * @param {string} target - 手机号或邮箱
	 */
	sendVerificationCode(target) {
		return request({
			url: '/api/verification/send',
			method: 'POST',
			data: { target }
		})
	},

	/**
	 * 获取余额明细
	 * @param {number} memberId - 会员ID
	 * @param {object} params - 查询参数
	 */
	getBalanceRecords(memberId, params = {}) {
		return request({
			url: `/api/members/${memberId}/balance-records`,
			method: 'GET',
			data: params
		})
	},

	/**
	 * 获取提现记录
	 * @param {number} memberId - 会员ID
	 * @param {object} params - 查询参数
	 */
	getWithdrawRecords(memberId, params = {}) {
		return request({
			url: `/api/members/${memberId}/withdraw-records`,
			method: 'GET',
			data: params
		})
	},

	/**
	 * 获取分佣记录
	 * @param {number} memberId - 会员ID
	 * @param {object} params - 查询参数
	 */
	getCommissionRecords(memberId, params = {}) {
		return request({
			url: `/api/members/${memberId}/commission-records`,
			method: 'GET',
			data: params
		})
	},

	/**
	 * 申请提现
	 * @param {number} memberId - 会员ID
	 * @param {object} withdrawData - 提现数据
	 */
	applyWithdraw(memberId, withdrawData) {
		return request({
			url: `/api/members/${memberId}/withdraw`,
			method: 'POST',
			data: withdrawData
		})
	}
}

// 用户状态管理
export const userStore = {
	/**
	 * 获取当前用户信息
	 */
	getCurrentUser() {
		return uni.getStorageSync('userInfo') || null
	},

	/**
	 * 设置当前用户信息
	 * @param {object} userInfo - 用户信息
	 */
	setCurrentUser(userInfo) {
		uni.setStorageSync('userInfo', userInfo)
	},

	/**
	 * 清除当前用户信息
	 */
	clearCurrentUser() {
		uni.removeStorageSync('userInfo')
		uni.removeStorageSync('token')
	},

	/**
	 * 检查是否已登录
	 */
	isLoggedIn() {
		const token = uni.getStorageSync('token')
		const userInfo = uni.getStorageSync('userInfo')
		return !!(token && userInfo)
	},

	/**
	 * 获取token
	 */
	getToken() {
		return uni.getStorageSync('token')
	},

	/**
	 * 设置token
	 * @param {string} token - 访问令牌
	 */
	setToken(token) {
		uni.setStorageSync('token', token)
	}
}

export default memberAPI
