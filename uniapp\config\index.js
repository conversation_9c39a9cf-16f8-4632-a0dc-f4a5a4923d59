/**
 * 全局配置文件
 */

// API基础URL配置
export const API_BASE_URL = 'http://localhost:3030'

// 图片服务URL配置 - 图片文件在后端服务器上
export const IMAGE_BASE_URL = 'http://localhost:5173'

// 开发模式配置
export const isDevelopment = process.env.NODE_ENV === 'development' || !process.env.NODE_ENV

// 🔧 日志控制开关 - 方便开发测试
// 设置为 true = 显示所有日志（开发测试时）
// 设置为 false = 隐藏日志（开发完成后）
export const enableConsoleLog = false // 👈 在这里切换日志开关

// 🎯 快速切换模式：
// 开发测试阶段：enableConsoleLog = true
// 开发完成后：enableConsoleLog = false

// 全局配置对象
export default {
  apiBaseUrl: API_BASE_URL,
  imageBaseUrl: IMAGE_BASE_URL,
  isDevelopment,
  enableConsoleLog
}