<template>
	<view class="register-page">
		<view class="register-container">
			<!-- 顶部Logo区域 -->
			<view class="logo-section">
				<view class="logo-icon">
					<text class="logo-text">🤖</text>
				</view>
				<text class="app-title">AI智能助手</text>
				<text class="app-subtitle">创建新账户</text>
			</view>

			<!-- 注册表单 -->
			<view class="form-section">
				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">📱</text>
						<input
							class="form-input"
							type="text"
							placeholder="请输入手机号或邮箱"
							v-model="registerForm.contact"
							:class="{ 'input-error': errors.contact }"
						/>
					</view>
					<text v-if="errors.contact" class="error-text">{{ errors.contact }}</text>
				</view>

				<view class="form-item">
					<view class="input-wrapper code-wrapper">
						<text class="input-icon">🔢</text>
						<input
							class="form-input code-input"
							type="text"
							placeholder="请输入验证码"
							v-model="registerForm.verificationCode"
							:class="{ 'input-error': errors.verificationCode }"
						/>
						<button
							class="code-btn"
							:class="{ 'btn-disabled': codeSending || codeCountdown > 0 }"
							@click="sendVerificationCode"
							:disabled="codeSending || codeCountdown > 0"
						>
							<text v-if="codeCountdown > 0">{{ codeCountdown }}s</text>
							<text v-else-if="codeSending">发送中</text>
							<text v-else>获取验证码</text>
						</button>
					</view>
					<text v-if="errors.verificationCode" class="error-text">{{ errors.verificationCode }}</text>
				</view>

				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🔒</text>
						<input 
							class="form-input" 
							:type="showPassword ? 'text' : 'password'" 
							placeholder="请输入密码"
							v-model="registerForm.password"
							:class="{ 'input-error': errors.password }"
						/>
						<text class="toggle-password" @click="togglePassword">
							{{ showPassword ? '👁️' : '🙈' }}
						</text>
					</view>
					<text v-if="errors.password" class="error-text">{{ errors.password }}</text>
				</view>

				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🔐</text>
						<input
							class="form-input"
							:type="showConfirmPassword ? 'text' : 'password'"
							placeholder="请确认密码"
							v-model="registerForm.confirmPassword"
							:class="{ 'input-error': errors.confirmPassword }"
						/>
						<text class="toggle-password" @click="toggleConfirmPassword">
							{{ showConfirmPassword ? '👁️' : '🙈' }}
						</text>
					</view>
					<text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
				</view>

					<!-- 邀请码输入框 -->
					<view class="form-item">
						<view class="input-wrapper">
							<text class="input-icon">🎫</text>
							<input
								class="form-input"
								type="text"
								placeholder="邀请码（选填）"
								v-model="registerForm.inviteCode"
								:disabled="inviteCodeLocked"
								:class="{ 'input-disabled': inviteCodeLocked }"
							/>
							<text v-if="inviteCodeLocked" class="lock-icon">🔒</text>
						</view>
						<text v-if="inviteCodeLocked" class="info-text">通过邀请链接注册，邀请码已自动填入</text>
						<text v-if="errors.inviteCode" class="error-text">{{ errors.inviteCode }}</text>
					</view>

				<!-- 用户协议 -->
				<view class="agreement-section">
					<view class="agreement-checkbox" @click="toggleAgreement">
						<text class="checkbox">{{ agreeToTerms ? '☑️' : '☐' }}</text>
						<text class="agreement-text">
							我已阅读并同意
							<text class="link-text" @click.stop="showTerms">《用户协议》</text>
							和
							<text class="link-text" @click.stop="showPrivacy">《隐私政策》</text>
						</text>
					</view>
					<text v-if="errors.agreement" class="error-text">{{ errors.agreement }}</text>
				</view>

				<!-- 注册按钮 -->
				<button 
					class="register-btn" 
					:class="{ 'btn-loading': isLoading }"
					@click="handleRegister"
					:disabled="isLoading"
				>
					<text v-if="isLoading">注册中...</text>
					<text v-else>注册</text>
				</button>

				<!-- 登录链接 -->
				<view class="login-link">
					<text class="link-text">已有账号？</text>
					<text class="link-action" @click="goToLogin">立即登录</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { memberAPI } from '@/api/members.js'

export default {
	data() {
		return {
			registerForm: {
				contact: '',
				verificationCode: '',
				password: '',
				confirmPassword: '',
				inviteCode: ''
			},
			errors: {},
			showPassword: false,
			showConfirmPassword: false,
			agreeToTerms: false,
			isLoading: false,
			codeSending: false,
			codeCountdown: 0,
			countdownTimer: null,
			isCodeVerified: false,
			inviteCodeLocked: false, // 邀请码是否锁定（通过邀请链接进入时锁定）
			inviterInfo: null // 邀请人信息
		}
	},

	onLoad(options) {
		console.log('注册页面加载，URL参数:', options)

		// 检查是否通过邀请链接进入
		if (options.inviteCode) {
			this.registerForm.inviteCode = options.inviteCode
			this.inviteCodeLocked = true
			console.log('通过邀请链接注册，邀请码:', options.inviteCode)

			// 如果有邀请人ID，可以获取邀请人信息
			if (options.inviter) {
				this.loadInviterInfo(options.inviter)
			}
		}
	},

	methods: {
		// 加载邀请人信息
		async loadInviterInfo(inviterId) {
			try {
				console.log('加载邀请人信息，ID:', inviterId)
				// 这里可以调用API获取邀请人信息
				// const response = await memberAPI.getMemberById(inviterId)
				// if (response && response.success) {
				//     this.inviterInfo = response.data
				//     console.log('邀请人信息:', this.inviterInfo)
				// }
			} catch (error) {
				console.error('加载邀请人信息失败:', error)
			}
		},

		// 切换密码显示
		togglePassword() {
			this.showPassword = !this.showPassword
		},

		// 切换确认密码显示
		toggleConfirmPassword() {
			this.showConfirmPassword = !this.showConfirmPassword
		},

		// 切换协议同意状态
		toggleAgreement() {
			this.agreeToTerms = !this.agreeToTerms
		},



		// 验证联系方式（手机号或邮箱）
		validateContact(contact) {
			if (!contact.trim()) {
				return '请输入手机号或邮箱'
			}

			const isPhone = /^1[3-9]\d{9}$/.test(contact);
			const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact);

			if (!isPhone && !isEmail) {
				return '请输入正确的手机号或邮箱地址'
			}
			return null
		},

		// 判断联系方式类型
		getContactType(contact) {
			const isPhone = /^1[3-9]\d{9}$/.test(contact);
			return isPhone ? 'phone' : 'email';
		},

		// 验证密码
		validatePassword(password) {
			if (!password.trim()) {
				return '请输入密码'
			}
			if (password.length < 6) {
				return '密码长度不能少于6位'
			}
			if (password.length > 20) {
				return '密码长度不能超过20位'
			}
			return null
		},

		// 验证邀请码
		validateInviteCode(inviteCode) {
			if (!inviteCode.trim()) {
				return null // 邀请码是选填的
			}

			// 邀请码格式：INV{userId}{timestamp}
			const inviteCodePattern = /^INV\d+\d{6}$/;
			if (!inviteCodePattern.test(inviteCode)) {
				return '邀请码格式不正确'
			}

			return null
		},

		// 验证表单
		validateForm() {
			this.errors = {}

			// 验证联系方式
			const contactError = this.validateContact(this.registerForm.contact)
			if (contactError) {
				this.errors.contact = contactError
			}

			// 验证验证码
			if (!this.registerForm.verificationCode.trim()) {
				this.errors.verificationCode = '请输入验证码'
			} else if (!this.isCodeVerified) {
				this.errors.verificationCode = '请先验证验证码'
			}

			// 验证密码
			const passwordError = this.validatePassword(this.registerForm.password)
			if (passwordError) {
				this.errors.password = passwordError
			}

			// 验证确认密码
			if (!this.registerForm.confirmPassword.trim()) {
				this.errors.confirmPassword = '请确认密码'
			} else if (this.registerForm.password !== this.registerForm.confirmPassword) {
				this.errors.confirmPassword = '两次输入的密码不一致'
			}

			// 验证邀请码（如果填写了）
			if (this.registerForm.inviteCode.trim()) {
				const inviteCodeError = this.validateInviteCode(this.registerForm.inviteCode)
				if (inviteCodeError) {
					this.errors.inviteCode = inviteCodeError
				}
			}

			// 验证协议同意
			if (!this.agreeToTerms) {
				this.errors.agreement = '请先同意用户协议和隐私政策'
			}

			return Object.keys(this.errors).length === 0
		},

		// 发送验证码
		async sendVerificationCode() {
			// 验证联系方式
			const contactError = this.validateContact(this.registerForm.contact)
			if (contactError) {
				this.errors.contact = contactError
				return
			}

			this.codeSending = true
			try {
				const response = await memberAPI.sendVerificationCode(this.registerForm.contact.trim())

				if (response && response.success) {
					uni.showToast({
						title: `验证码已发送到${this.getContactType(this.registerForm.contact) === 'phone' ? '手机' : '邮箱'}`,
						icon: 'success'
					})

					// 开始倒计时
					this.startCountdown()
				} else {
					uni.showToast({
						title: response.message || '发送失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('发送验证码失败:', error)
				uni.showToast({
					title: '发送失败，请重试',
					icon: 'none'
				})
			} finally {
				this.codeSending = false
			}
		},

		// 开始倒计时
		startCountdown() {
			this.codeCountdown = 60
			this.countdownTimer = setInterval(() => {
				this.codeCountdown--
				if (this.codeCountdown <= 0) {
					clearInterval(this.countdownTimer)
					this.countdownTimer = null
				}
			}, 1000)
		},

		// 验证验证码
		async verifyVerificationCode() {
			if (!this.registerForm.verificationCode.trim()) {
				this.errors.verificationCode = '请输入验证码'
				return
			}

			try {
				const response = await memberAPI.verifyCode(
					this.registerForm.contact.trim(),
					this.registerForm.verificationCode.trim()
				)

				if (response && response.success) {
					this.isCodeVerified = true
					this.errors.verificationCode = ''
					uni.showToast({
						title: '验证码验证成功',
						icon: 'success'
					})
				} else {
					this.isCodeVerified = false
					this.errors.verificationCode = response.message || '验证码错误'
				}
			} catch (error) {
				console.error('验证验证码失败:', error)
				this.isCodeVerified = false
				this.errors.verificationCode = '验证失败，请重试'
			}
		},

		// 处理注册
		async handleRegister() {
			// 如果验证码未验证，先验证验证码
			if (!this.isCodeVerified && this.registerForm.verificationCode.trim()) {
				await this.verifyVerificationCode()
			}

			if (!this.validateForm()) {
				return
			}

			this.isLoading = true

			try {
				const contactType = this.getContactType(this.registerForm.contact)

				// 准备注册数据
				const registerData = {
					contact: this.registerForm.contact.trim(),
					verificationCode: this.registerForm.verificationCode.trim(),
					phone: contactType === 'phone' ? this.registerForm.contact.trim() : undefined,
					password: this.registerForm.password,
					email: contactType === 'email' ? this.registerForm.contact.trim() : undefined,
					name: this.registerForm.contact.trim() // 使用联系方式作为用户名
				}

				// 如果有邀请码，添加到注册数据中
				if (this.registerForm.inviteCode.trim()) {
					registerData.inviteCode = this.registerForm.inviteCode.trim()
					console.log('注册时包含邀请码:', registerData.inviteCode)
				}

				// 使用会员管理API注册新会员
				const response = await memberAPI.register(registerData)

				if (response && response.success) {
					uni.showToast({
						title: '注册成功',
						icon: 'success'
					})

					// 延迟跳转到登录页面
					setTimeout(() => {
						uni.navigateBack({
							fail: (err) => {
								console.error('返回失败:', err)
								// 如果navigateBack失败，直接跳转到登录页
								uni.redirectTo({
									url: '/pages/auth/login'
								})
							}
						})
					}, 1500)
				} else {
					uni.showToast({
						title: response.message || '注册失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('注册错误:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			} finally {
				this.isLoading = false
			}
		},

		// 跳转到登录页面
		goToLogin() {
			uni.navigateBack({
				fail: (err) => {
					console.error('返回失败:', err)
					// 如果navigateBack失败，直接跳转到登录页
					uni.redirectTo({
						url: '/pages/auth/login'
					})
				}
			})
		},

		// 显示用户协议
		showTerms() {
			uni.showModal({
				title: '用户协议',
				content: '这里是用户协议的内容...',
				showCancel: false
			})
		},

		// 显示隐私政策
		showPrivacy() {
			uni.showModal({
				title: '隐私政策',
				content: '这里是隐私政策的内容...',
				showCancel: false
			})
		},

		// 页面销毁时清理定时器
		beforeDestroy() {
			if (this.countdownTimer) {
				clearInterval(this.countdownTimer)
				this.countdownTimer = null
			}
		}
	}
}
</script>

<style scoped>
.register-page {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.register-container {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 24rpx;
	padding: 60rpx 40rpx;
	width: 100%;
	max-width: 600rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

.logo-section {
	text-align: center;
	margin-bottom: 60rpx;
}

.logo-icon {
	margin-bottom: 20rpx;
}

.logo-text {
	font-size: 80rpx;
}

.app-title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.app-subtitle {
	display: block;
	font-size: 28rpx;
	color: #666;
}

.form-section {
	margin-bottom: 40rpx;
}

.form-item {
	margin-bottom: 30rpx;
}

.input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.input-wrapper:focus-within {
	border-color: #667eea;
	background: #fff;
}

.input-icon {
	padding: 0 20rpx;
	font-size: 32rpx;
	color: #999;
}

.form-input {
	flex: 1;
	padding: 24rpx 20rpx;
	font-size: 32rpx;
	border: none;
	background: transparent;
	color: #333;
}

.form-input.input-error {
	border-color: #ff4757;
}

.toggle-password {
	padding: 0 20rpx;
	font-size: 32rpx;
	color: #999;
	cursor: pointer;
}

.input-disabled {
	background-color: #f5f5f5 !important;
	color: #999 !important;
}

.lock-icon {
	font-size: 28rpx;
	color: #999;
	padding: 0 20rpx;
}

.info-text {
	font-size: 24rpx;
	color: #666;
	margin-top: 10rpx;
	margin-left: 20rpx;
}

.error-text {
	color: #ff4757;
	font-size: 24rpx;
	margin-top: 10rpx;
	margin-left: 20rpx;
}

.agreement-section {
	margin-bottom: 40rpx;
}

.agreement-checkbox {
	display: flex;
	align-items: flex-start;
	cursor: pointer;
}

.checkbox {
	font-size: 32rpx;
	margin-right: 10rpx;
	margin-top: 2rpx;
}

.agreement-text {
	flex: 1;
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

.link-text {
	color: #667eea;
	cursor: pointer;
}

.register-btn {
	width: 100%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 28rpx;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 30rpx;
	transition: all 0.3s ease;
}

.register-btn:active {
	transform: translateY(2rpx);
}

.register-btn.btn-loading {
	opacity: 0.7;
}

.login-link {
	text-align: center;
}

.link-action {
	font-size: 28rpx;
	color: #667eea;
	margin-left: 10rpx;
	cursor: pointer;
}

.code-wrapper {
	position: relative;
}

.code-input {
	padding-right: 140rpx !important;
}

.code-btn {
	position: absolute;
	right: 10rpx;
	top: 50%;
	transform: translateY(-50%);
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 20rpx;
	font-size: 24rpx;
	min-width: 120rpx;
	text-align: center;
	transition: all 0.3s ease;
}

.code-btn:active {
	transform: translateY(-50%) scale(0.95);
}

.code-btn.btn-disabled {
	background: #ccc;
	color: #999;
}
</style>
