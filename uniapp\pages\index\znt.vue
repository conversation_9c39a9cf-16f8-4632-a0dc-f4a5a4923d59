<template>
	<view class="container">

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- AI助手卡片 -->
			<view class="ai-card">
				<view class="ai-avatar-container">
					<image class="ai-avatar" :src="agentAvatar" mode="aspectFill"></image>
				</view>
			</view>

			<!-- 欢迎消息 -->
			<view class="welcome-message">
				<text class="welcome-title">{{ welcomeTitle }}</text>
				<text class="welcome-desc">{{ welcomeDesc }}</text>
			</view>


			<!-- 多媒体内容卡片 -->
			<view class="media-cards">
				<!-- 案例视频展示 -->
				<view v-if="mediaContents.video.enabled" class="media-section">
					<view class="media-card video-card">
						<view class="media-header">
							<view class="media-type-icon">
								<text class="type-icon">📹</text>
							</view>
							<text class="media-title">{{ mediaContents.video.title }}</text>
						</view>
						<view class="image-container">
							<video
								class="media-image"
								:src="mediaContents.video.content"
								controls
								show-center-play-btn
								show-play-btn
								object-fit="fill"
								:style="{
									width: '100%',
									height: videoHeight + 'px'
								}"
								@loadedmetadata="onVideoLoaded"
								@timeupdate="onVideoTimeUpdate"
								@canplay="onVideoCanPlay"
								@error="onVideoError"
							></video>
						</view>
					</view>
				</view>

				<!-- 案例图片展示 -->
				<view v-if="mediaContents.image.enabled" class="media-section">
					<view class="media-card image-card">
						<view class="media-header">
							<view class="media-type-icon">
								<text class="type-icon">🖼️</text>
							</view>
							<text class="media-title">{{ mediaContents.image.title }}</text>
						</view>
						<view class="image-container">
							<image
								class="media-image"
								:src="mediaContents.image.content"
								mode="widthFix"
								@load="onImageLoaded"
							></image>
						</view>
					</view>
				</view>

				<!-- 文字教学 -->
				<view v-if="mediaContents.text.enabled" class="media-section">
					<view class="media-card text-card">
						<view class="media-header">
							<view class="media-type-icon">
								<text class="type-icon">📄</text>
							</view>
							<text class="media-title">{{ mediaContents.text.title }}</text>
						</view>
						<view class="text-content-wrapper">
							<text class="text-teaching-content">{{ mediaContents.text.content }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 开始聊天按钮和功能按钮组 -->
		<view class="start-chat-container">
			<!-- 历史记录按钮 -->
			<view class="function-btn history-btn" @click="openHistoryModal">
				<view class="btn-icon">📖</view>
			</view>

			<!-- 开始聊天按钮 -->
			<view class="start-chat-btn" @click="openChatModal">
				<view class="chat-icon">💬</view>
				<text class="chat-text">开始对话</text>
			</view>

			<!-- 分享按钮 -->
			<view class="function-btn share-btn" @click="openShareModal">
				<view class="btn-icon">📤</view>
			</view>
		</view>



		<!-- 历史记录弹窗 -->
		<view v-if="showHistoryModal" class="modal-overlay" @click="closeHistoryModal">
			<view class="history-modal" @click.stop>
				<!-- 弹窗头部 -->
				<view class="modal-header">
					<text class="modal-title">聊天历史</text>
					<view class="close-btn" @click="closeHistoryModal">✕</view>
				</view>

				<!-- 历史记录列表 -->
				<view class="history-content">
					<view v-if="historyLoading" class="loading-container">
						<text class="loading-text">加载中...</text>
					</view>

					<view v-else-if="historyList.length === 0" class="empty-container">
						<text class="empty-text">当前无更多智能体记录</text>
					</view>

					<view v-else class="history-list">
						<view
							v-for="item in historyList"
							:key="item.id"
							class="history-item"
							@click="viewHistoryDetail(item)"
						>
							<view class="history-time">{{ formatTime(item.createdAt) }}</view>
							<view class="history-preview">
								<text class="user-message">我：{{ truncateText(item.userMessage, 50) }}</text>
								<text class="ai-message">{{ item.agentName || 'AI智能体' }}：{{ truncateText(item.assistantReply, 50) }}</text>
							</view>
							<!-- 复制按钮 -->
							<view class="copy-btn" @click.stop="copyHistoryContent(item)">
								<text class="copy-icon">📋</text>
							</view>
						</view>
					</view>

					<!-- 加载更多 -->
					<view v-if="hasMoreHistory" class="load-more" @click="loadMoreHistory">
						<text class="load-more-text">加载更多</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 分享弹窗 -->
		<view v-if="showShareModal" class="modal-overlay" @click="closeShareModal">
			<view class="share-modal" @click.stop>
				<!-- 弹窗头部 -->
				<view class="modal-header">
					<text class="modal-title">分享智能体</text>
					<view class="close-btn" @click="closeShareModal">✕</view>
				</view>

				<!-- 分享内容 -->
				<view class="share-content">
					<view class="share-info">
						<view class="agent-info">
							<image class="agent-avatar" :src="agentInfo.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
							<view class="agent-details">
								<text class="agent-name">{{ agentInfo.name }}</text>
								<text class="agent-desc">{{ agentInfo.description }}</text>
							</view>
						</view>
					</view>

					<!-- 分享方式 -->
					<view class="share-methods">
						<!-- 微信分享 -->
						<view class="share-method" @click="shareToWechat">
							<view class="method-icon">💬</view>
							<text class="method-text">微信分享</text>
						</view>

						<!-- 生成邀请码 -->
						<view class="share-method" @click="generateShareCode">
							<view class="method-icon">🔗</view>
							<text class="method-text">生成邀请码</text>
						</view>

						<!-- 复制链接 -->
						<view class="share-method" @click="copyShareLink">
							<view class="method-icon">📋</view>
							<text class="method-text">复制链接</text>
						</view>
					</view>

					<!-- 邀请码显示 -->
					<view v-if="shareCode" class="share-code-container">
						<text class="share-code-label">邀请码：</text>
						<view class="share-code-box">
							<text class="share-code">{{ shareCode }}</text>
							<view class="copy-code-btn" @click="copyShareCode">复制</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 聊天弹窗 -->
		<view v-if="showChatModal" class="chat-modal-overlay" @click="closeChatModal">
			<view class="chat-modal" @click.stop>
				<!-- 弹窗头部 -->
				<view class="modal-header">
					<view class="header-info">
						<view class="points-info">
							<text class="points-text">当前剩余点数：{{ remainingPoints || 0 }}</text>
							<text class="consumption-text">当前每条消息扣除点数：{{ consumption || 0 }}</text>
						</view>
					</view>
					<view class="close-btn" @click="closeChatModal">
						<text class="close-icon">✕</text>
					</view>
				</view>

				<!-- 聊天消息区域 -->
				<view class="modal-chat-container">
					<!-- 欢迎消息 -->
					<view v-if="messages.length === 0" class="welcome-message">
						<view class="welcome-avatar">
							<image class="avatar" :src="agentAvatar" mode="aspectFill"></image>
						</view>
						<view class="welcome-content">
							<text class="welcome-text">请先查看主页面上的具体流程</text>
						</view>
					</view>

					<!-- 聊天消息列表 -->
					<view class="modal-messages-container" id="modalMessagesContainer">
						<view
							v-for="(message, index) in messages"
							:key="message.id"
							class="modal-message-item"
							:class="message.role === 'user' ? 'user-message' : 'assistant-message'"
						>
							<!-- 用户消息 -->
							<template v-if="message.role === 'user'">
								<view class="modal-message-content">
									<view class="modal-message-bubble user-bubble" :class="{ 'uploading-bubble': message.isUploading }">
										<!-- 图片消息 -->
										<view v-if="message.isImage && message.imageUrl" class="image-message">
											<image
												:src="message.imageUrl"
												mode="widthFix"
												class="message-image"
												@click="previewImage(message.imageUrl)"
												@load="onImageLoad"
												@error="onImageError"
												lazy-load
											></image>
											<view class="image-actions">
												<text class="copy-btn" @click="copyImageUrl(message.imageUrl)">复制链接</text>
											</view>
										</view>
										<!-- 文件消息 -->
										<view v-else-if="message.isFile" class="file-message">
											<view class="file-info">
												<text class="file-icon">📁</text>
												<view class="file-details">
													<text class="file-name">{{ message.fileName }}</text>
													<text class="file-url" @click="copyToClipboard(message.fileUrl)">点击复制链接</text>
												</view>
											</view>
										</view>
										<!-- 普通文本消息 -->
										<text v-else class="modal-message-text">{{ message.content }}</text>
									</view>
								</view>
								<view class="modal-message-avatar">
									<image
										class="avatar"
										src="/static/user-avatar.png"
										mode="aspectFill"
									></image>
								</view>
							</template>

							<!-- 助手消息 -->
							<template v-else>
								<view class="modal-message-avatar">
									<image
										class="avatar"
										:src="agentAvatar"
										mode="aspectFill"
									></image>
								</view>
								<view class="modal-message-content">
									<view class="modal-message-bubble assistant-bubble" :class="{ 'error-bubble': message.isError }">
										<!-- 图片消息 -->
										<view v-if="message.isImage && message.imageUrl" class="image-message">
											<image
												:src="message.imageUrl"
												mode="widthFix"
												class="message-image"
												@click="previewImage(message.imageUrl)"
												@load="onImageLoad"
												@error="onImageError"
												lazy-load
											></image>
											<view class="image-actions">
												<text class="copy-btn" @click="copyImageUrl(message.imageUrl)">复制链接</text>
											</view>
										</view>
										<!-- 兼容旧的Markdown格式图片 -->
										<view v-else-if="isImageMessage(message.content)" class="image-message">
											<image
												:src="extractImageUrl(message.content)"
												mode="widthFix"
												class="message-image"
												@click="previewImage(extractImageUrl(message.content))"
											></image>
											<view class="image-actions">
												<text class="copy-btn" @click="copyImageUrl(extractImageUrl(message.content))">复制链接</text>
											</view>
										</view>
										<text v-else class="modal-message-text">{{ message.content }}</text>
									</view>
								</view>
							</template>
						</view>

						<!-- AI正在工作中的提示 -->
						<view v-if="isWaitingResponse" class="modal-message-item assistant-message working-message">
							<view class="modal-message-avatar">
								<image
									class="avatar"
									:src="agentAvatar"
									mode="aspectFill"
								></image>
							</view>
							<view class="modal-message-content">
								<view class="modal-message-bubble working-bubble">
									<view class="working-indicator">
										<view class="loading-dots">
											<view class="dot"></view>
											<view class="dot"></view>
											<view class="dot"></view>
										</view>
										<text class="working-text">正在工作中...</text>
									</view>
								</view>
							</view>
						</view>
					</view>


				</view>

				<!-- 弹窗输入区域 -->
				<view class="modal-input-container">
					<view class="modal-input-wrapper">
						<view class="modal-add-btn" @click="showAttachmentOptions">
							<text class="add-icon">+</text>
						</view>
						<input
							class="modal-message-input"
							v-model="inputMessage"
							placeholder="输入您的消息..."
							:disabled="isSending"
							@confirm="sendMessage"
							confirm-type="send"
						/>
						<!-- 停止按钮（在等待响应时显示） -->
						<view
							v-if="isWaitingResponse"
							class="modal-stop-btn"
							@click="stopResponse"
						>
							<text class="stop-icon">⏹</text>
						</view>
						<!-- 发送按钮（在非等待状态时显示） -->
						<view
							v-else
							class="modal-send-btn"
							:class="{ 'disabled': !inputMessage.trim() || isSending }"
							@click="sendMessage"
						>
							<text v-if="!isSending">发送</text>
							<text v-else>发送中...</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 附件选择菜单 -->
			<view v-if="showAttachmentMenu" class="attachment-menu-overlay" @click="closeAttachmentMenu">
				<view class="attachment-menu" @click.stop>
					<view class="menu-header">
						<text class="menu-title">选择附件类型</text>
					</view>
					<view class="attachment-option" @click="takePhoto">
						<text class="option-icon">📷</text>
						<view class="option-content">
							<text class="option-text">拍照</text>
							<text class="option-desc">使用相机拍摄照片</text>
						</view>
					</view>
					<view class="attachment-option" @click="chooseImage">
						<text class="option-icon">🖼️</text>
						<view class="option-content">
							<text class="option-text">从相册选择</text>
							<text class="option-desc">选择已有的图片</text>
						</view>
					</view>
					<view class="attachment-option disabled-option" @click="showFileUploadTip">
						<text class="option-icon">📁</text>
						<view class="option-content">
							<text class="option-text">发送文件</text>
							<text class="option-desc">功能开发中...</text>
						</view>
					</view>
					<view class="attachment-option cancel-option" @click="closeAttachmentMenu">
						<text class="option-text">取消</text>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 自定义点数不足弹窗 -->
	<view v-if="showPointsModal" class="points-modal-overlay" @click="closePointsModal">
		<view class="points-modal" @click.stop>
			<!-- 弹窗头部 -->
			<view class="points-modal-header">
				<view class="points-icon">💎</view>
				<text class="points-title">点数不足</text>
				<view class="points-close-btn" @click="closePointsModal">
					<text class="points-close-icon">✕</text>
				</view>
			</view>

			<!-- 弹窗内容 -->
			<view class="points-modal-content">
				<view class="points-info-card">
					<view class="points-current">
						<text class="points-label">当前剩余</text>
						<text class="points-value">{{ remainingPoints || 0 }}</text>
						<text class="points-unit">点数</text>
					</view>
					<view class="points-divider"></view>
					<view class="points-required">
						<text class="points-label">执行需要</text>
						<text class="points-value required">{{ requiredPointsForModal || 0 }}</text>
						<text class="points-unit">点数</text>
					</view>
				</view>

				<view class="points-message">
					<text class="message-text">执行此智能体需要 {{ requiredPointsForModal || 0 }} 点数，您当前剩余 {{ remainingPoints || 0 }} 点数。</text>
					<text class="message-sub">请购买套餐获取更多点数继续使用。</text>
				</view>
			</view>

			<!-- 弹窗按钮 -->
			<view class="points-modal-actions">
				<view class="points-btn points-btn-cancel" @click="closePointsModal">
					<text class="points-btn-text">取消</text>
				</view>
				<view class="points-btn points-btn-primary" @click="handleBuyPackage">
					<text class="points-btn-text">购买套餐</text>
				</view>
			</view>
		</view>
	</view>


</template>

<script>
import { cozeApi, COZE_MESSAGE_ROLES } from '@/api/coze-api.js'
import agentThemeManager from '@/api/agent-themes.js'
import { IMAGE_BASE_URL, API_BASE_URL } from '@/config/index.js'
import { userStore } from '@/api/members.js'
import chatHistoryApi from '@/api/chat-history.js'
import shareApi from '@/api/share.js'

export default {
	data() {
		return {
			// 聊天相关数据
			messages: [], // 聊天消息列表
			inputMessage: '', // 当前输入的消息
			isLoading: false, // 是否正在加载
			isSending: false, // 是否正在发送消息
			isWaitingResponse: false, // 是否正在等待AI响应
			currentChatId: null, // 当前聊天ID
			currentConversationId: null, // 当前对话ID
			showChatModal: false, // 是否显示聊天弹窗
			showAttachmentMenu: false, // 是否显示附件菜单

			// 历史记录相关
			showHistoryModal: false, // 是否显示历史记录弹窗
			historyList: [], // 历史记录列表
			historyLoading: false, // 历史记录加载状态
			historyPage: 1, // 历史记录页码
			hasMoreHistory: true, // 是否还有更多历史记录

			// 分享相关
			showShareModal: false, // 是否显示分享弹窗
			shareCode: '', // 分享邀请码
			shareConfig: null, // 分享配置

			// 自定义弹窗相关
			showPointsModal: false, // 是否显示点数不足弹窗
			requiredPointsForModal: 0, // 弹窗显示的所需点数

			// 智能体配置
			currentTheme: null, // 当前智能体主题
			botId: null, // 当前智能体ID
			appId: null, // 应用ID

			// 用户配置
			userId: 'user_' + Date.now(), // 用户ID
			conversationId: null, // 对话ID，用于维持对话上下文

			// UI状态
			showWelcome: true, // 是否显示欢迎消息
			errorMessage: '', // 错误消息
			isApiInitialized: false, // API是否已初始化

			// 点数相关
			remainingPoints: 0, // 当前剩余点数
			consumption: 1, // 当前每条消息扣除点数

			// 页面显示数据
			agentTitle: '智能体标题', // 智能体标题
			agentSubtitle: '消息提分：2', // 智能体副标题
			agentName: '智能体标题', // 智能体名称
			agentDescription: '智能体描述', // 智能体描述
			agentAvatar: '/static/images/znt_avatar.png', // 智能体头像
			welcomeTitle: 'AI智能体', // 欢迎标题
			welcomeDesc: '智能体描述', // 欢迎描述

			// 多媒体内容数据 - 三个固定类型
			mediaContents: {
				// 案例视频展示
				video: {
					enabled: true, // 是否启用
					title: '案例视频展示',
					content: 'https://example.com/demo_video.mp4',
					description: '观看实际操作演示视频'
				},
				// 案例图片展示
				image: {
					enabled: true, // 是否启用
					title: '案例图片展示',
					content: '/static/images/znt_avatar.png', // 暂时使用现有图片
					description: '查看详细图片案例说明'
				},
				// 文字教学
				text: {
					enabled: true, // 是否启用
					title: '文字教学',
					content: '这里是详细的文字教学内容，包含操作步骤、注意事项和相关说明。用户可以通过阅读这些内容来学习如何使用相关功能...',
					description: '阅读详细的操作指南'
				}
			},

			// 重试相关
			retryCount: 0, // 重试次数
			maxRetries: 3, // 最大重试次数

			// 网络状态
			isOnline: true, // 是否在线

			// 视频自适应高度
			videoHeight: 250, // 默认高度

			// API引用
			cozeApi: cozeApi,

			// 用户相关
			isLoggedIn: false, // 是否已登录
			userInfo: null, // 用户信息

			// 魔法导航栏相关
			// navItems: [
			// 	{ text: '初页', icon: 'icon-home', path: '/pages/index/index' },
			// 	{ text: '同创', icon: 'icon-robot', path: '/pages/index/znt' },
			// 	{ text: '会享', icon: 'icon-message', path: '/pages/chat/index' },
			// 	{ text: '往档', icon: 'icon-history', path: '/pages/history/index' },
			// 	{ text: '吾界', icon: 'icon-user', path: '/pages/profile/index' }
			// ]
		}
	},

	async onLoad(options) {
		console.log('=== znt.vue onLoad 开始 ===')
		console.log('接收到的参数:', options)
		console.log('参数类型:', typeof options)
		console.log('参数键值:', options ? Object.keys(options) : 'null')

		// 检查用户登录状态
		this.checkUserLoginStatus()

		// 处理邀请码参数
		if (options && options.inviter) {
			console.log('检测到邀请人ID:', options.inviter)
			// 如果用户已登录，建立邀请关系
			if (userStore.isLoggedIn()) {
				await this.handleInviterRelation(options.inviter)
			} else {
				// 保存邀请人信息，等用户登录后处理
				uni.setStorageSync('pendingInviter', options.inviter)
			}
		}

		// 处理邀请码参数
		if (options && options.inviteCode) {
			console.log('检测到邀请码:', options.inviteCode)
			await this.handleInviteCodeAccess(options.inviteCode)
		}

		// 获取应用ID（首页传递的参数名是id）
		if (options && options.id) {
			this.appId = options.id
			console.log('✅ 设置应用ID:', this.appId)
		} else {
			console.warn('⚠️ 未接收到应用ID参数，将使用默认配置')
			console.log('当前URL参数:', window.location ? window.location.search : 'N/A')
		}

		// 初始化默认图片URL
		this.initializeImageUrls()

		// 监听网络状态
		this.setupNetworkListener()
		await this.initializeChat()
	},

	onUnload() {
		// 清理网络监听
		uni.offNetworkStatusChange()
	},

	// 微信小程序分享配置
	// #ifdef MP-WEIXIN
	onShareAppMessage() {
		const currentUser = userStore.getCurrentUser()
		const shareConfig = this.shareConfig || {}

		return {
			title: shareConfig.share_title || 'AI智能体使用邀请',
			desc: shareConfig.share_description || '注册即可获得额外免费使用次数，快来体验智能AI助手！',
			imageUrl: shareConfig.share_image_url || '',
			path: `/pages/index/znt?id=${this.appId}&inviter=${currentUser?.id || ''}`
		}
	},

	onShareTimeline() {
		const currentUser = userStore.getCurrentUser()
		const shareConfig = this.shareConfig || {}

		return {
			title: shareConfig.share_title || 'AI智能体使用邀请',
			imageUrl: shareConfig.share_image_url || '',
			query: `id=${this.appId}&inviter=${currentUser?.id || ''}`
		}
	},
	// #endif

	computed: {
		// 智能体信息
		agentInfo() {
			return {
				name: this.currentTheme?.name || '智能助手',
				description: this.currentTheme?.description || '您的AI智能助手',
				avatar: this.currentTheme?.avatar || '/static/default-avatar.png'
			}
		}
	},

	methods: {


		// 初始化图片URL
		initializeImageUrls() {
			// 处理图片内容URL
			if (this.mediaContents.image.content && this.mediaContents.image.content.startsWith('/')) {
				this.mediaContents.image.content = IMAGE_BASE_URL + this.mediaContents.image.content
			}
			console.log('✅ 初始化图片URL完成:', {
				imageContent: this.mediaContents.image.content
			})
		},





		// 处理多媒体内容点击
		handleMediaClick(type) {
			console.log('点击多媒体内容:', type)

			const media = this.mediaContents[type]
			if (!media || !media.enabled) {
				return
			}

			if (type === 'video') {
				// 处理案例视频展示
				this.playVideo(media)
			} else if (type === 'image') {
				// 处理案例图片展示
				this.previewImage(media)
			} else if (type === 'text') {
				// 处理文字教学
				this.expandText(media)
			}
		},

		// 播放案例视频
		playVideo(media) {
			uni.showToast({
				title: '播放' + media.title,
				icon: 'none'
			})
			// 这里可以集成视频播放器或跳转到视频页面
		},

		// 预览案例图片
		previewImage(media) {
			uni.previewImage({
				urls: [media.content],
				current: media.content
			})
		},

		// 展开文字教学
		expandText(media) {
			uni.showModal({
				title: media.title,
				content: media.content,
				showCancel: false,
				confirmText: '知道了'
			})
		},

		// 更新多媒体内容配置（从后端数据）
		updateMediaContents(agentData) {
			console.log('🔄 更新多媒体内容配置，后端数据:', agentData)
			if (!agentData) return

			// 从 introContents 字段中获取多媒体内容
			let introContents = agentData.introContents
			console.log('📋 原始 introContents 数据:', introContents)
			console.log('📋 introContents 类型:', typeof introContents)

			// 处理数组格式的 introContents
			let processedContents = {}

			if (Array.isArray(introContents)) {
				console.log('📋 introContents 是数组，开始处理...')
				// 将数组转换为对象格式
				introContents.forEach((item, index) => {
					console.log(`📋 处理第 ${index + 1} 项:`, item)
					if (item && item.type && item.value) {
						processedContents[item.type] = item.value
						console.log(`✅ 添加 ${item.type}:`, item.value)
					}
				})
			} else if (typeof introContents === 'string') {
				// 如果是字符串，尝试解析为 JSON
				if (introContents.trim() === '') {
					processedContents = {}
					console.log('📋 introContents 为空字符串，设为空对象')
				} else {
					try {
						const parsed = JSON.parse(introContents)
						if (Array.isArray(parsed)) {
							parsed.forEach((item) => {
								if (item && item.type && item.value) {
									processedContents[item.type] = item.value
								}
							})
						} else {
							processedContents = parsed
						}
						console.log('📋 解析后的 introContents:', processedContents)
					} catch (error) {
						console.error('❌ 解析 introContents JSON 失败:', error)
						processedContents = {}
					}
				}
			} else if (introContents && typeof introContents === 'object') {
				// 如果已经是对象，直接使用
				processedContents = introContents
			} else {
				// 其他情况设为空对象
				processedContents = {}
			}

			console.log('📋 最终处理的内容:', processedContents)

			// 检查是否有数据
			const isEmpty = Object.keys(processedContents).length === 0
			console.log('📋 处理后的内容是否为空:', isEmpty)

			// 更新案例视频展示
			console.log('🔍 检查 video 字段:', processedContents.video, '类型:', typeof processedContents.video)
			if (processedContents.video && processedContents.video.trim() !== '') {
				console.log('✅ 有视频数据，启用视频展示:', processedContents.video)
				this.mediaContents.video.enabled = true
				// 处理视频URL - 如果是相对路径则添加 IMAGE_BASE_URL
				let videoUrl = processedContents.video
				if (videoUrl.startsWith('/')) {
					videoUrl = IMAGE_BASE_URL + videoUrl
				}
				this.mediaContents.video.content = videoUrl
				console.log('📹 最终视频URL:', videoUrl)
			} else {
				console.log('❌ 无视频数据，禁用视频展示，video 字段:', processedContents.video)
				this.mediaContents.video.enabled = false
			}

			// 更新案例图片展示
			console.log('🔍 检查 image 字段:', processedContents.image, '类型:', typeof processedContents.image)
			if (processedContents.image && processedContents.image.trim() !== '') {
				console.log('✅ 有图片数据，启用图片展示:', processedContents.image)
				this.mediaContents.image.enabled = true
				// 处理图片URL，如果是相对路径则添加图片服务器地址
				if (processedContents.image.startsWith('/')) {
					this.mediaContents.image.content = IMAGE_BASE_URL + processedContents.image
				} else {
					this.mediaContents.image.content = processedContents.image
				}
			} else {
				console.log('❌ 无图片数据，禁用图片展示，image 字段:', processedContents.image)
				this.mediaContents.image.enabled = false
			}

			// 更新文字教学
			console.log('🔍 检查 text 字段:', processedContents.text, '类型:', typeof processedContents.text)
			if (processedContents.text && processedContents.text.trim() !== '') {
				console.log('✅ 有后端文字数据，启用文字教学:', processedContents.text)
				this.mediaContents.text.enabled = true
				this.mediaContents.text.content = processedContents.text
			} else {
				console.log('❌ 无后端文字数据，禁用文字教学')
				this.mediaContents.text.enabled = false
			}
			console.log('🔍 最终 mediaContents.text 状态:', this.mediaContents.text)

			console.log('📋 多媒体内容配置更新完成:', this.mediaContents)

			// 强制触发视图更新
			this.$forceUpdate()
		},

		// 初始化聊天
		async initializeChat() {
			try {
				this.isLoading = true
				this.errorMessage = ''

				console.log('=== 开始初始化聊天 ===')
				console.log('当前应用ID:', this.appId)
				console.log('API基础URL:', API_BASE_URL)

				// 检查网络连接
				const isConnected = await this.checkNetworkConnection()
				if (!isConnected) {
					throw new Error('网络连接不可用，请检查网络设置')
				}

				// 使用重试机制获取智能体主题配置
				await this.callApiWithRetry(async () => {
					console.log('=== 开始获取智能体主题配置 ===')
					console.log('传入的应用ID:', this.appId)

					// 如果有传入的应用ID，优先根据ID获取对应的智能体配置
					if (this.appId) {
						console.log('🔍 根据应用ID获取智能体配置:', this.appId)
						try {
							this.currentTheme = await agentThemeManager.apiClient.getAgentThemeById(this.appId)
							console.log('✅ 成功获取智能体主题配置:', this.currentTheme)
							console.log('主题配置类型:', typeof this.currentTheme)
							console.log('主题配置详情:', JSON.stringify(this.currentTheme, null, 2))
						} catch (error) {
							console.warn('❌ 根据应用ID获取智能体配置失败:', error)
							console.warn('错误详情:', {
								message: error.message,
								name: error.name,
								stack: error.stack
							})
							this.currentTheme = null
						}
					} else {
						console.warn('⚠️ 未提供应用ID，将使用默认配置')
					}

					// 如果没有获取到配置，则使用页面默认配置
					if (!this.currentTheme) {
						console.log('🔄 使用页面默认配置')
						const currentPage = '/pages/index/znt'
						this.currentTheme = await agentThemeManager.getThemeForPage(currentPage)
						console.log('默认配置结果:', this.currentTheme)
					}

					if (this.currentTheme) {
						this.botId = agentThemeManager.apiClient.getBotIdFromTheme(this.currentTheme)
						console.log('✅ 智能体主题配置获取成功')
						console.log('最终主题配置:', this.currentTheme)
						console.log('提取的智能体ID:', this.botId)

						// 更新页面信息
						console.log('🔄 准备调用 updatePageInfo')
						this.updatePageInfo()
						console.log('✅ updatePageInfo 调用完成')
					} else {
						console.warn('⚠️ 未找到匹配的智能体主题，使用默认配置')
						// 使用您实际的智能体ID
						this.botId = '7491537356286459913' // 您的智能体ID
					}

					// 确保botId不为空
					if (!this.botId) {
						console.warn('⚠️ botId为空，使用默认ID')
						this.botId = '7491537356286459913' // 您的智能体ID
					}

					console.log('🎯 最终确定的智能体ID:', this.botId)
					console.log('=== 智能体主题配置获取结束 ===')
				})

				// 检查Coze API是否已初始化
				if (!cozeApi.isInitialized()) {
					console.log('Coze API未初始化，尝试从系统配置加载')
					await this.callApiWithRetry(async () => {
						await cozeApi.loadFromSystemConfig()
					})
				}

				// 更新API初始化状态
				this.isApiInitialized = cozeApi.isInitialized()

				// 重置重试计数
				this.resetRetryCount()

				console.log('聊天初始化成功')

				// 预加载视频尺寸信息
				this.preloadVideoInfo()

			} catch (error) {
				console.error('初始化聊天失败:', error)
				console.error('错误详情:', {
					message: error.message,
					name: error.name,
					stack: error.stack,
					appId: this.appId,
					apiBaseUrl: API_BASE_URL
				})
				this.errorMessage = this.getErrorMessage(error)
				this.showErrorToast('初始化失败: ' + error.message)
			} finally {
				this.isLoading = false
			}
		},

		// 发送消息
		async sendMessage() {
			console.log('=== 开始发送消息 ===')
			console.log('输入消息:', this.inputMessage)
			console.log('是否正在发送:', this.isSending)
			console.log('智能体ID:', this.botId)

			if (!this.inputMessage.trim() || this.isSending) {
				console.log('消息为空或正在发送，取消发送')
				return
			}

			if (!this.botId) {
				console.log('智能体未配置')
				uni.showToast({
					title: '智能体未配置',
					icon: 'error'
				})
				return
			}

			// 检查用户点数
			if (this.remainingPoints <= 0) {
				console.log('用户点数不足，需要登录')
				// 使用自定义弹窗替代原生弹窗
				this.showPointsInsufficientModal(this.consumption || 1)
				return
			}

			const userMessage = this.inputMessage.trim()
			this.inputMessage = ''
			this.showWelcome = false

			console.log('用户消息:', userMessage)
			console.log('清空输入框，隐藏欢迎消息')

			// 添加用户消息到聊天记录
			this.messages.push({
				id: Date.now(),
				role: COZE_MESSAGE_ROLES.USER,
				content: userMessage,
				timestamp: new Date()
			})

			console.log('添加用户消息到聊天记录，当前消息数量:', this.messages.length)
			console.log('当前消息列表:', this.messages)

			// 强制更新视图
			this.$forceUpdate()

			// 滚动到底部
			this.$nextTick(() => {
				this.scrollToBottom()
			})

			try {
				this.isSending = true
				this.isWaitingResponse = true

				// 检查网络连接
				const isConnected = await this.checkNetworkConnection()
				if (!isConnected) {
					throw new Error('网络连接不可用')
				}

				// 准备发送给API的消息格式
				const apiMessages = this.messages.map(msg => ({
					role: msg.role,
					content: msg.content,
					content_type: 'text'
				}))

				// 使用重试机制发送聊天请求
				const response = await this.callApiWithRetry(async () => {
					const chatOptions = {
						userId: this.userId
					}

					// 如果有对话ID，则添加到选项中
					if (this.conversationId) {
						chatOptions.conversationId = this.conversationId
					}

					return await cozeApi.chat(this.botId, apiMessages, chatOptions)
				})

				console.log('聊天响应:', response)
				console.log('响应类型:', typeof response)
				console.log('响应结构:', JSON.stringify(response, null, 2))

				// 保存聊天ID用于取消功能
				if (response && response.data) {
					this.currentChatId = response.data.chat_id
					this.currentConversationId = response.data.conversation_id
					console.log('保存聊天ID:', this.currentChatId, '对话ID:', this.currentConversationId)
				}

				// 处理响应并添加助手回复
				let assistantReply = null

				// 处理不同的响应格式
				if (response && typeof response.code !== 'undefined') {
					// Coze API v3格式: { code: 0, msg: "success", data: {...} }
					console.log('检测到Coze API v3格式，code:', response.code)

					if (response.code === 0) {
						// 成功响应，保存conversation_id
						if (response.data && response.data.conversation_id && !this.conversationId) {
							this.conversationId = response.data.conversation_id
							console.log('💾 保存对话ID:', this.conversationId)
						}

						// 解析回复内容
						if (response.data && response.data.content) {
							console.log('使用Coze API v3 data.content格式解析')
							assistantReply = response.data.content
						} else if (response.data && response.data.messages && response.data.messages.length > 0) {
							// 可能的消息数组格式
							console.log('使用Coze API v3 data.messages格式解析')
							const lastMessage = response.data.messages[response.data.messages.length - 1]
							assistantReply = lastMessage.content || lastMessage.text
						} else if (response.data && typeof response.data === 'string') {
							console.log('使用Coze API v3 data字符串格式解析')
							assistantReply = response.data
						} else {
							console.error('Coze API成功响应但无法解析内容:', response.data)
							throw new Error('API响应成功但无法解析回复内容')
						}
					} else {
						// 错误响应
						const errorMsg = response.msg || response.message || `API错误 (code: ${response.code})`
						console.error('Coze API返回错误:', errorMsg)
						throw new Error(errorMsg)
					}
				} else if (response && response.choices && response.choices.length > 0) {
					// OpenAI格式
					console.log('使用OpenAI格式解析')
					assistantReply = response.choices[0].message.content
				} else if (response && response.content) {
					// 直接content格式
					console.log('使用直接content格式解析')
					assistantReply = response.content
				} else if (response && response.message) {
					// message格式
					console.log('使用message格式解析')
					assistantReply = response.message
				} else if (typeof response === 'string') {
					// 字符串格式
					console.log('使用字符串格式解析')
					assistantReply = response
				} else {
					console.error('未识别的响应格式:', response)
					console.error('响应的所有属性:', Object.keys(response || {}))
					throw new Error('无效的响应格式')
				}

				console.log('解析出的助手回复:', assistantReply)

				if (assistantReply) {
					// 检查回复是否包含图片
					const isImageReply = this.isImageMessage(assistantReply)
					const imageUrl = isImageReply ? this.extractImageUrl(assistantReply) : null

					// 添加AI回复
					const assistantMessage = {
						id: Date.now() + 1,
						role: COZE_MESSAGE_ROLES.ASSISTANT,
						content: assistantReply,
						timestamp: new Date()
					}

					// 如果是图片回复，添加图片属性
					if (isImageReply && imageUrl) {
						assistantMessage.isImage = true
						assistantMessage.imageUrl = imageUrl
					}

					this.messages.push(assistantMessage)

					// 🎯 聊天成功后立即扣除点数显示
					console.log('💰 聊天成功，立即扣除点数显示')
					console.log('扣除前点数:', this.remainingPoints)
					console.log('扣除点数:', this.consumption || 500)

					// 立即更新本地点数显示
					const deductPoints = this.consumption || 500
					this.remainingPoints = Math.max(0, this.remainingPoints - deductPoints)
					console.log('扣除后点数:', this.remainingPoints)

					// 成功收到回复，重置等待状态
					this.isWaitingResponse = false
					this.currentChatId = null
					this.currentConversationId = null
				} else {
					throw new Error('响应中没有找到有效内容')
				}

			} catch (error) {
				console.error('发送消息失败:', error)

				// 获取友好的错误消息
				const errorMessage = this.getErrorMessage(error)

				// 添加错误消息
				this.messages.push({
					id: Date.now() + 1,
					role: COZE_MESSAGE_ROLES.ASSISTANT,
					content: `抱歉，${errorMessage}`,
					timestamp: new Date(),
					isError: true
				})

				this.showErrorToast('发送失败')

				// 发送失败时重置等待状态
				this.isWaitingResponse = false
				this.currentChatId = null
				this.currentConversationId = null
			} finally {
				this.isSending = false

				// 滚动到底部
				this.$nextTick(() => {
					this.scrollToBottom()
				})
			}
		},

		// 滚动到底部
		scrollToBottom() {
			// 使用uni-app的滚动API
			uni.pageScrollTo({
				scrollTop: 999999,
				duration: 300
			})
		},

		// 处理输入框回车事件
		onInputConfirm() {
			this.sendMessage()
		},





		// 格式化时间
		formatTime(timestamp) {
			const date = new Date(timestamp)
			const now = new Date()
			const diff = now - date

			if (diff < 60000) { // 1分钟内
				return '刚刚'
			} else if (diff < 3600000) { // 1小时内
				return Math.floor(diff / 60000) + '分钟前'
			} else if (diff < 86400000) { // 24小时内
				return Math.floor(diff / 3600000) + '小时前'
			} else {
				return date.toLocaleDateString()
			}
		},

		// 检查是否是图片消息（Markdown格式）
		isImageMessage(content) {
			if (!content) return false
			// 检查Markdown图片格式: ![alt](url)
			const markdownImagePattern = /!\[.*?\]\((https?:\/\/.*?\.(png|jpg|jpeg|gif|webp)(\?.*)?)\)/i
			return markdownImagePattern.test(content)
		},

		// 检查是否是纯URL消息
		isUrlMessage(content) {
			if (!content) return false
			// 检查是否是纯图片URL
			const imageUrlPattern = /^https?:\/\/.*\.(png|jpg|jpeg|gif|webp)(\?.*)?$/i
			return imageUrlPattern.test(content.trim())
		},

		// 从Markdown格式中提取图片URL
		extractImageUrl(content) {
			if (!content) return ''

			// 先尝试Markdown格式
			const markdownMatch = content.match(/!\[.*?\]\((https?:\/\/.*?\.(png|jpg|jpeg|gif|webp)(\?.*)?)\)/i)
			if (markdownMatch) {
				return markdownMatch[1]
			}

			// 如果是纯URL，直接返回
			const imageUrlPattern = /^https?:\/\/.*\.(png|jpg|jpeg|gif|webp)(\?.*)?$/i
			if (imageUrlPattern.test(content.trim())) {
				return content.trim()
			}

			return ''
		},

		// 预览图片
		previewImage(imageUrl) {
			if (!imageUrl) return

			uni.previewImage({
				urls: [imageUrl],
				current: imageUrl,
				fail: (err) => {
					console.error('预览图片失败:', err)
					uni.showToast({
						title: '预览失败',
						icon: 'none'
					})
				}
			})
		},

		// 复制图片URL
		copyImageUrl(imageUrl) {
			this.copyToClipboard(imageUrl)
		},

		// 图片加载成功
		onImageLoad(event) {
			console.log('图片加载成功:', event)
		},

		// 图片加载失败
		onImageError(event) {
			console.error('图片加载失败:', event)
			uni.showToast({
				title: '图片加载失败',
				icon: 'none',
				duration: 2000
			})
		},

		// 预览图片
		previewImage(imageUrl) {
			uni.previewImage({
				urls: [imageUrl],
				current: imageUrl
			})
		},

		// 打开聊天弹窗
		async openChatModal() {
			console.log('尝试打开聊天弹窗')

			// 获取用户点数信息
			await this.fetchUserPoints()

			// 检查用户点数
			if (this.remainingPoints <= 0) {
				console.log('用户点数不足，需要登录')
				// 使用自定义弹窗替代原生弹窗
				this.showPointsInsufficientModal(this.consumption || 1)
				return
			}

			this.showChatModal = true
			console.log('打开聊天弹窗')

			// 延迟滚动到底部，确保弹窗完全显示
			this.$nextTick(() => {
				setTimeout(() => {
					this.scrollModalToBottom()
				}, 100)
			})
		},

		// 关闭聊天弹窗
		closeChatModal() {
			this.showChatModal = false
			console.log('关闭聊天弹窗')
		},

		// 打开历史记录弹窗
		async openHistoryModal() {
			console.log('打开历史记录弹窗')

			// 检查用户登录状态
			if (!userStore.isLoggedIn()) {
				uni.showModal({
					title: '提示',
					content: '请先登录查看聊天历史',
					confirmText: '去登录',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/login'
							})
						}
					}
				})
				return
			}

			this.showHistoryModal = true
			this.historyPage = 1
			this.hasMoreHistory = true
			await this.loadChatHistory()
		},

		// 关闭历史记录弹窗
		closeHistoryModal() {
			this.showHistoryModal = false
			this.historyList = []
			console.log('关闭历史记录弹窗')
		},

		// 加载聊天历史
		async loadChatHistory() {
			if (this.historyLoading) return

			this.historyLoading = true
			try {
				const response = await chatHistoryApi.getChatHistory({
					page: this.historyPage,
					pageSize: 20
				})

				if (response.code === 200) {
					const newRecords = response.data.records || []
					if (this.historyPage === 1) {
						this.historyList = newRecords
					} else {
						this.historyList.push(...newRecords)
					}

					// 检查是否还有更多数据
					this.hasMoreHistory = newRecords.length === 20
				}
			} catch (error) {
				console.error('加载聊天历史失败:', error)
				// 确保在错误情况下historyList为空数组，显示空状态
				if (this.historyPage === 1) {
					this.historyList = []
				}
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				this.historyLoading = false
			}
		},

		// 加载更多历史记录
		async loadMoreHistory() {
			if (!this.hasMoreHistory || this.historyLoading) return

			this.historyPage++
			await this.loadChatHistory()
		},

		// 查看历史记录详情
		viewHistoryDetail(item) {
			console.log('查看历史记录详情:', item)
			const agentName = item.agentName || 'AI智能体'
			uni.showModal({
				title: '聊天记录',
				content: `用户：${item.userMessage}\n\n${agentName}：${item.assistantReply}`,
				showCancel: false
			})
		},

		// 复制历史记录内容（只复制括号内的内容）
		copyHistoryContent(item) {
			console.log('复制历史记录内容:', item)

			// 提取括号内的内容
			let contentToCopy = ''
			const assistantReply = item.assistantReply || ''

			// 优先查找URL链接（通常在圆括号中）
			const urlPattern = /\(([^)]*(?:https?:\/\/|www\.)[^)]*)\)/g
			const urlMatches = [...assistantReply.matchAll(urlPattern)]

			if (urlMatches.length > 0) {
				// 如果找到URL，优先使用URL
				contentToCopy = urlMatches.map(match => match[1]).filter(content => content.trim()).join('\n')
			} else {
				// 如果没有URL，查找其他括号内的内容
				const bracketPatterns = [
					/\(([^)]+)\)/g,  // 圆括号 ()
					/\[([^\]]+)\]/g, // 方括号 []
					/\{([^}]+)\}/g,  // 花括号 {}
					/（([^）]+)）/g,  // 中文圆括号 （）
					/【([^】]+)】/g   // 中文方括号 【】
				]

				// 尝试匹配各种括号模式
				let matches = []
				for (const pattern of bracketPatterns) {
					const patternMatches = [...assistantReply.matchAll(pattern)]
					matches = matches.concat(patternMatches)
				}

				if (matches.length > 0) {
					// 如果找到括号内容，提取所有匹配的内容
					const extractedContents = matches.map(match => match[1]).filter(content => content.trim())
					if (extractedContents.length > 0) {
						contentToCopy = extractedContents.join('\n')
					}
				}
			}

			// 如果没有找到括号内容，则复制整个AI回复
			if (!contentToCopy) {
				contentToCopy = assistantReply
			}

			console.log('准备复制的内容:', contentToCopy)

			// 使用uni.setClipboardData复制到剪贴板
			uni.setClipboardData({
				data: contentToCopy,
				success: () => {
					uni.showToast({
						title: '已复制到剪贴板',
						icon: 'success',
						duration: 2000
					})
				},
				fail: () => {
					uni.showToast({
						title: '复制失败',
						icon: 'none',
						duration: 2000
					})
				}
			})
		},

		// 打开分享弹窗
		async openShareModal() {
			console.log('打开分享弹窗')

			// 检查用户登录状态
			if (!userStore.isLoggedIn()) {
				uni.showModal({
					title: '提示',
					content: '请先登录使用分享功能',
					confirmText: '去登录',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/auth/login'
							})
						}
					}
				})
				return
			}

			this.showShareModal = true
			this.shareCode = ''

			// 加载分享配置
			try {
				const response = await shareApi.getShareConfig()
				if (response.code === 200) {
					this.shareConfig = response.data
				}
			} catch (error) {
				console.error('加载分享配置失败:', error)
			}
		},

		// 关闭分享弹窗
		closeShareModal() {
			this.showShareModal = false
			this.shareCode = ''
			console.log('关闭分享弹窗')
		},

		// 微信分享
		shareToWechat() {
			console.log('微信分享')

			// 获取分享配置
			const shareTitle = this.shareConfig?.share_title || 'AI智能体使用邀请'
			const shareDesc = this.shareConfig?.share_description || '注册即可获得额外免费使用次数，快来体验智能AI助手！'
			const shareImageUrl = this.shareConfig?.share_image_url || ''

			// 构建分享路径，包含邀请人信息
			const currentUser = userStore.getCurrentUser()
			const sharePath = `/pages/index/znt?inviter=${currentUser.id}&botId=${this.botId}`

			const shareConfig = shareApi.getWechatShareConfig({
				title: shareTitle,
				desc: shareDesc,
				imageUrl: shareImageUrl,
				path: sharePath
			})

			// 微信小程序分享
			// #ifdef MP-WEIXIN
			uni.showShareMenu({
				withShareTicket: true,
				success: () => {
					console.log('分享菜单显示成功')
				}
			})
			// #endif

			// 其他平台提示
			// #ifndef MP-WEIXIN
			uni.showToast({
				title: '请在微信小程序中使用分享功能',
				icon: 'none'
			})
			// #endif
		},

		// 生成分享邀请码
		async generateShareCode() {
			console.log('生成分享邀请码')

			try {
				uni.showLoading({
					title: '生成中...'
				})

				const response = await shareApi.generateInviteCode({
					type: 'agent',
					targetId: this.botId || 'default'
				})

				if (response.code === 200) {
					this.shareCode = response.data.inviteCode
					uni.showToast({
						title: '邀请码生成成功',
						icon: 'success'
					})
				} else {
					throw new Error(response.message || '生成失败')
				}
			} catch (error) {
				console.error('生成邀请码失败:', error)
				uni.showToast({
					title: '生成失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		// 复制分享链接
		copyShareLink() {
			console.log('复制分享链接')

			const currentUser = userStore.getCurrentUser()
			const shareUrl = `${API_BASE_URL}/share?inviter=${currentUser.id}&botId=${this.botId}`

			this.copyToClipboard(shareUrl)
		},

		// 复制邀请码
		copyShareCode() {
			if (!this.shareCode) {
				uni.showToast({
					title: '请先生成邀请码',
					icon: 'none'
				})
				return
			}

			this.copyToClipboard(this.shareCode)
		},

		// 截取文本
		truncateText(text, maxLength) {
			if (!text) return ''
			if (text.length <= maxLength) return text
			return text.substring(0, maxLength) + '...'
		},

		// 处理邀请人关系
		async handleInviterRelation(inviterId) {
			try {
				console.log('处理邀请人关系:', inviterId)
				// 这里可以调用API建立邀请关系
				// 暂时只记录日志，具体实现可以根据需要添加
			} catch (error) {
				console.error('处理邀请人关系失败:', error)
			}
		},

		// 处理邀请码访问
		async handleInviteCodeAccess(inviteCode) {
			try {
				console.log('处理邀请码访问:', inviteCode)

				const response = await shareApi.handleInviteCode(inviteCode)
				if (response.code === 200) {
					console.log('邀请码处理成功:', response.data)

					// 如果需要登录，提示用户
					if (response.data.needLogin) {
						uni.showModal({
							title: '欢迎使用',
							content: '请登录以获得邀请奖励',
							confirmText: '去登录',
							cancelText: '稍后',
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/auth/login'
									})
								}
							}
						})
					} else {
						uni.showToast({
							title: '欢迎使用！',
							icon: 'success'
						})
					}
				}
			} catch (error) {
				console.error('处理邀请码失败:', error)
			}
		},

		// 显示附件选项
		showAttachmentOptions() {
			console.log('点击了+号按钮')
			this.showAttachmentMenu = true
		},

		// 关闭附件菜单
		closeAttachmentMenu() {
			this.showAttachmentMenu = false
		},

		// 显示文件上传提示
		showFileUploadTip() {
			this.closeAttachmentMenu()
			uni.showToast({
				title: '文件上传功能开发中',
				icon: 'none',
				duration: 2000
			})
		},

		// 拍照
		async takePhoto() {
			this.closeAttachmentMenu()
			try {
				console.log('开始拍照...')
				const res = await uni.chooseImage({
					count: 1,
					sourceType: ['camera'],
					sizeType: ['compressed']
				})

				console.log('拍照结果:', res)

				// 处理不同平台的返回格式
				let tempFilePaths = null
				if (res.tempFilePaths && res.tempFilePaths.length > 0) {
					// 标准格式
					tempFilePaths = res.tempFilePaths
				} else if (res[1] && res[1].tempFilePaths && res[1].tempFilePaths.length > 0) {
					// 某些平台的格式
					tempFilePaths = res[1].tempFilePaths
				}

				if (tempFilePaths && tempFilePaths.length > 0) {
					const imagePath = tempFilePaths[0]
					console.log('拍照成功，图片路径:', imagePath)
					await this.uploadAndSendImage(imagePath, '拍照图片')
				} else {
					console.error('未获取到图片路径，完整响应:', res)
					throw new Error('未获取到图片路径')
				}
			} catch (error) {
				console.error('拍照失败:', error)
				uni.showToast({
					title: '拍照失败',
					icon: 'none'
				})
			}
		},

		// 从相册选择
		async chooseImage() {
			this.closeAttachmentMenu()
			try {
				console.log('开始选择图片...')
				const res = await uni.chooseImage({
					count: 1,
					sourceType: ['album'],
					sizeType: ['compressed']
				})

				console.log('选择图片结果:', res)

				// 处理不同平台的返回格式
				let tempFilePaths = null
				if (res.tempFilePaths && res.tempFilePaths.length > 0) {
					// 标准格式
					tempFilePaths = res.tempFilePaths
				} else if (res[1] && res[1].tempFilePaths && res[1].tempFilePaths.length > 0) {
					// 某些平台的格式
					tempFilePaths = res[1].tempFilePaths
				}

				if (tempFilePaths && tempFilePaths.length > 0) {
					const imagePath = tempFilePaths[0]
					console.log('选择图片成功，图片路径:', imagePath)
					await this.uploadAndSendImage(imagePath, '相册图片')
				} else {
					console.error('未获取到图片路径，完整响应:', res)
					throw new Error('未获取到图片路径')
				}
			} catch (error) {
				console.error('选择图片失败:', error)
				uni.showToast({
					title: '选择图片失败',
					icon: 'none'
				})
			}
		},

		// 选择文件
		async chooseFile() {
			this.closeAttachmentMenu()
			// #ifdef H5
			try {
				// H5环境使用input file
				const input = document.createElement('input')
				input.type = 'file'
				input.accept = '*/*'
				input.onchange = async (event) => {
					const file = event.target.files[0]
					if (file) {
						console.log('H5选择文件成功:', file)
						await this.uploadAndSendFile(file, file.name)
					}
				}
				input.click()
			} catch (error) {
				console.error('H5选择文件失败:', error)
				uni.showToast({
					title: '选择文件失败',
					icon: 'none'
				})
			}
			// #endif
			// #ifndef H5
			try {
				const res = await uni.chooseFile({
					count: 1,
					extension: ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.txt', '.zip']
				})

				if (res[1] && res[1].tempFiles && res[1].tempFiles.length > 0) {
					const file = res[1].tempFiles[0]
					console.log('选择文件成功:', file)
					await this.uploadAndSendFile(file.path, file.name)
				}
			} catch (error) {
				console.error('选择文件失败:', error)
				uni.showToast({
					title: '选择文件失败',
					icon: 'none'
				})
			}
			// #endif
		},

		// 上传并发送图片
		async uploadAndSendImage(imagePath, description = '图片') {
			try {
				uni.showLoading({
					title: '上传图片中...'
				})

				// 先添加一个占位消息显示正在上传
				const placeholderMessage = {
					id: Date.now(),
					role: COZE_MESSAGE_ROLES.USER,
					content: `[正在上传${description}...]`,
					timestamp: new Date(),
					isUploading: true
				}
				this.messages.push(placeholderMessage)
				this.scrollModalToBottom()

				// 上传图片到扣子服务器
				const uploadRes = await uni.uploadFile({
					url: `${API_BASE_URL}/api/upload/coze-image`,
					filePath: imagePath,
					name: 'file',
					header: {
						'Authorization': `Bearer ${userStore.getToken()}`
					}
				})

				uni.hideLoading()

				console.log('上传响应:', uploadRes)

				if (uploadRes.statusCode === 200) {
					const result = JSON.parse(uploadRes.data)
					if ((result.code === 0 || result.code === 1) && result.data) {
						// 移除占位消息
						const index = this.messages.findIndex(msg => msg.id === placeholderMessage.id)
						if (index !== -1) {
							this.messages.splice(index, 1)
						}

						// 添加图片消息
						const imageMessage = {
							id: Date.now(),
							role: COZE_MESSAGE_ROLES.USER,
							content: `![${description}](${result.data.url})`,
							timestamp: new Date(),
							isImage: true,
							imageUrl: result.data.url,
							file_id: result.data.file_id, // 扣子文件ID
							coze_file_info: result.data.coze_file_info // 扣子文件信息
						}
						this.messages.push(imageMessage)
						this.scrollModalToBottom()

						// 发送图片消息给AI
						await this.sendImageToAI(imageMessage)
					} else {
						throw new Error(result.message || result.msg || '上传失败')
					}
				} else {
					console.error('上传请求失败，状态码:', uploadRes.statusCode, '响应:', uploadRes.data)
					throw new Error(`上传请求失败，状态码: ${uploadRes.statusCode}`)
				}
			} catch (error) {
				uni.hideLoading()
				console.error('上传图片失败:', error)
				console.error('错误详情:', {
					message: error.message,
					stack: error.stack,
					uploadRes: error.uploadRes || '无上传响应'
				})

				// 移除占位消息
				const index = this.messages.findIndex(msg => msg.id === placeholderMessage.id)
				if (index !== -1) {
					this.messages.splice(index, 1)
				}

				uni.showToast({
					title: '图片上传失败',
					icon: 'none'
				})
			}
		},

		// 上传并发送文件
		async uploadAndSendFile(filePath, fileName) {
			try {
				uni.showLoading({
					title: '上传文件中...'
				})

				// 先添加一个占位消息显示正在上传
				const placeholderMessage = {
					id: Date.now(),
					role: COZE_MESSAGE_ROLES.USER,
					content: `[正在上传文件: ${fileName}...]`,
					timestamp: new Date(),
					isUploading: true
				}
				this.messages.push(placeholderMessage)
				this.scrollModalToBottom()

				// 上传文件到扣子服务器
				const uploadRes = await uni.uploadFile({
					url: `${API_BASE_URL}/api/upload/coze-file`,
					filePath: filePath,
					name: 'file',
					header: {
						'Authorization': `Bearer ${userStore.getToken()}`
					}
				})

				uni.hideLoading()

				console.log('文件上传响应:', uploadRes)

				if (uploadRes.statusCode === 200) {
					const result = JSON.parse(uploadRes.data)
					if ((result.code === 0 || result.code === 1) && result.data) {
						// 移除占位消息
						const index = this.messages.findIndex(msg => msg.id === placeholderMessage.id)
						if (index !== -1) {
							this.messages.splice(index, 1)
						}

						// 添加文件消息
						const fileMessage = {
							id: Date.now(),
							role: COZE_MESSAGE_ROLES.USER,
							content: `📁 ${fileName}`,
							timestamp: new Date(),
							isFile: true,
							fileName: fileName,
							fileUrl: result.data.url,
							file_id: result.data.file_id, // 扣子文件ID
							coze_file_info: result.data.coze_file_info // 扣子文件信息
						}
						this.messages.push(fileMessage)
						this.scrollModalToBottom()

						// 发送文件信息给AI
						await this.sendFileToAI(fileMessage)
					} else {
						throw new Error(result.message || result.msg || '上传失败')
					}
				} else {
					throw new Error('上传请求失败')
				}
			} catch (error) {
				uni.hideLoading()
				console.error('上传文件失败:', error)

				// 移除占位消息
				const index = this.messages.findIndex(msg => msg.id === placeholderMessage.id)
				if (index !== -1) {
					this.messages.splice(index, 1)
				}

				uni.showToast({
					title: '文件上传失败',
					icon: 'none'
				})
			}
		},

		// 弹窗内滚动到底部
		scrollModalToBottom() {
			try {
				const query = uni.createSelectorQuery().in(this)
				query.select('#modalMessagesContainer').boundingClientRect((data) => {
					if (data) {
						uni.pageScrollTo({
							scrollTop: data.height,
							duration: 300
						})
					}
				}).exec()
			} catch (error) {
				console.error('弹窗滚动失败:', error)
			}
		},

		// 发送图片给AI
		async sendImageToAI(imageMessage) {
			try {
				this.isSending = true
				this.isWaitingResponse = true

				// 检查网络连接
				const isConnected = await this.checkNetworkConnection()
				if (!isConnected) {
					throw new Error('网络连接不可用')
				}

				// 准备发送给API的消息格式
				const apiMessages = this.messages
					.filter(msg => !msg.isUploading) // 过滤掉上传中的消息
					.map(msg => {
						if (msg.isImage && msg.file_id) {
							// 如果是图片且有扣子文件ID，使用扣子格式
							return {
								role: msg.role,
								content: [
									{
										type: 'text',
										text: '请帮我分析这张图片'
									},
									{
										type: 'image',
										file_id: msg.file_id
									}
								],
								content_type: 'object_string'
							}
						} else {
							// 普通文本消息
							return {
								role: msg.role,
								content: msg.content,
								content_type: 'text'
							}
						}
					})

				// 发送聊天请求
				const response = await this.callApiWithRetry(async () => {
					const chatOptions = {
						userId: this.userId
					}

					if (this.currentConversationId) {
						chatOptions.conversation_id = this.currentConversationId
					}

					return await cozeApi.chat(this.botId, apiMessages, chatOptions)
				})

				// 处理AI响应
				if (response && response.data) {
					this.currentChatId = response.data.chat_id
					this.currentConversationId = response.data.conversation_id

					let assistantReply = null
					if (response.data.content) {
						assistantReply = response.data.content
					} else if (response.data.messages && response.data.messages.length > 0) {
						const lastMessage = response.data.messages[response.data.messages.length - 1]
						assistantReply = lastMessage.content || lastMessage.text
					}

					if (assistantReply) {
						// 检查回复是否包含图片
						const isImageReply = this.isImageMessage(assistantReply)
						const imageUrl = isImageReply ? this.extractImageUrl(assistantReply) : null

						// 添加AI回复
						const assistantMessage = {
							id: Date.now() + 1,
							role: COZE_MESSAGE_ROLES.ASSISTANT,
							content: assistantReply,
							timestamp: new Date()
						}

						// 如果是图片回复，添加图片属性
						if (isImageReply && imageUrl) {
							assistantMessage.isImage = true
							assistantMessage.imageUrl = imageUrl
						}

						this.messages.push(assistantMessage)

						// 扣除点数
						const deductPoints = this.consumption || 1
						this.remainingPoints = Math.max(0, this.remainingPoints - deductPoints)
						this.scrollModalToBottom()
					}
				}
			} catch (error) {
				console.error('发送图片给AI失败:', error)
				this.messages.push({
					id: Date.now() + 1,
					role: COZE_MESSAGE_ROLES.ASSISTANT,
					content: '抱歉，图片分析失败，请稍后重试。',
					timestamp: new Date(),
					isError: true
				})
			} finally {
				this.isSending = false
				this.isWaitingResponse = false
				this.scrollModalToBottom()
			}
		},

		// 发送文件给AI
		async sendFileToAI(fileMessage) {
			try {
				this.isSending = true
				this.isWaitingResponse = true

				// 检查网络连接
				const isConnected = await this.checkNetworkConnection()
				if (!isConnected) {
					throw new Error('网络连接不可用')
				}

				// 准备发送给API的消息格式
				const apiMessages = this.messages
					.filter(msg => !msg.isUploading) // 过滤掉上传中的消息
					.map(msg => {
						if (msg.isFile && msg.file_id) {
							// 如果是文件且有扣子文件ID，使用扣子格式
							return {
								role: msg.role,
								content: [
									{
										type: 'text',
										text: `请帮我分析这个文件：${msg.fileName}`
									},
									{
										type: 'file',
										file_id: msg.file_id
									}
								],
								content_type: 'object_string'
							}
						} else {
							// 普通文本消息
							return {
								role: msg.role,
								content: msg.content,
								content_type: 'text'
							}
						}
					})

				// 发送聊天请求
				const response = await this.callApiWithRetry(async () => {
					const chatOptions = {
						userId: this.userId
					}

					if (this.currentConversationId) {
						chatOptions.conversation_id = this.currentConversationId
					}

					return await cozeApi.chat(this.botId, apiMessages, chatOptions)
				})

				// 处理AI响应
				if (response && response.data) {
					this.currentChatId = response.data.chat_id
					this.currentConversationId = response.data.conversation_id

					let assistantReply = null
					if (response.data.content) {
						assistantReply = response.data.content
					} else if (response.data.messages && response.data.messages.length > 0) {
						const lastMessage = response.data.messages[response.data.messages.length - 1]
						assistantReply = lastMessage.content || lastMessage.text
					}

					if (assistantReply) {
						// 检查回复是否包含图片
						const isImageReply = this.isImageMessage(assistantReply)
						const imageUrl = isImageReply ? this.extractImageUrl(assistantReply) : null

						// 添加AI回复
						const assistantMessage = {
							id: Date.now() + 1,
							role: COZE_MESSAGE_ROLES.ASSISTANT,
							content: assistantReply,
							timestamp: new Date()
						}

						// 如果是图片回复，添加图片属性
						if (isImageReply && imageUrl) {
							assistantMessage.isImage = true
							assistantMessage.imageUrl = imageUrl
						}

						this.messages.push(assistantMessage)

						// 扣除点数
						const deductPoints = this.consumption || 1
						this.remainingPoints = Math.max(0, this.remainingPoints - deductPoints)
						this.scrollModalToBottom()
					}
				}
			} catch (error) {
				console.error('发送文件给AI失败:', error)
				this.messages.push({
					id: Date.now() + 1,
					role: COZE_MESSAGE_ROLES.ASSISTANT,
					content: '抱歉，文件分析失败，请稍后重试。',
					timestamp: new Date(),
					isError: true
				})
			} finally {
				this.isSending = false
				this.isWaitingResponse = false
				this.scrollModalToBottom()
			}
		},

		// 停止AI响应
		async stopResponse() {
			if (!this.currentChatId || !this.currentConversationId) {
				console.log('没有活跃的聊天请求可以取消')
				return
			}

			try {
				console.log('🛑 用户请求停止响应')

				// 调用后端取消聊天API
				const response = await uni.request({
					url: `${API_BASE_URL}/api/coze-proxy/cancel-chat`,
					method: 'POST',
					data: {
						conversation_id: this.currentConversationId,
						chat_id: this.currentChatId
					},
					header: {
						'Content-Type': 'application/json'
					}
				})

				console.log('取消聊天响应:', response)

				if (response.statusCode === 200 && response.data.code === 0) {
					// 成功取消
					console.log('✅ 聊天请求已成功取消')

					// 添加取消消息
					this.messages.push({
						id: Date.now(),
						role: COZE_MESSAGE_ROLES.ASSISTANT,
						content: '响应已停止',
						timestamp: new Date(),
						isCancelled: true
					})

					uni.showToast({
						title: '已停止响应',
						icon: 'success'
					})
				} else {
					console.log('⚠️ 取消聊天请求失败:', response.data?.msg || '未知错误')
					uni.showToast({
						title: '停止失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('❌ 停止响应失败:', error)
				uni.showToast({
					title: '停止失败',
					icon: 'none'
				})
			} finally {
				// 重置状态
				this.isWaitingResponse = false
				this.isSending = false
				this.currentChatId = null
				this.currentConversationId = null
				this.$forceUpdate()
			}
		},

		// 复制到剪贴板
		copyToClipboard(text) {
			if (!text) return

			uni.setClipboardData({
				data: text,
				success: () => {
					uni.showToast({
						title: '已复制到剪贴板',
						icon: 'success',
						duration: 1500
					})
				},
				fail: (err) => {
					console.error('复制失败:', err)
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					})
				}
			})
		},

		// 设置网络状态监听
		setupNetworkListener() {
			// 获取当前网络状态
			uni.getNetworkType({
				success: (res) => {
					this.isOnline = res.networkType !== 'none'
				}
			})

			// 监听网络状态变化
			uni.onNetworkStatusChange((res) => {
				this.isOnline = res.isConnected

				if (!res.isConnected) {
					uni.showToast({
						title: '网络连接已断开',
						icon: 'error'
					})
				} else {
					uni.showToast({
						title: '网络连接已恢复',
						icon: 'success'
					})

					// 网络恢复后，如果有错误状态，尝试重新初始化
					if (this.errorMessage) {
						this.retryInitialize()
					}
				}
			})
		},

		// 重试初始化
		async retryInitialize() {
			if (this.retryCount < this.maxRetries) {
				this.retryCount++
				console.log(`重试初始化，第${this.retryCount}次`)
				await this.initializeChat()
			} else {
				this.errorMessage = '初始化失败次数过多，请检查网络连接后手动重试'
			}
		},

		// 重置重试计数
		resetRetryCount() {
			this.retryCount = 0
		},

		// 检查网络连接
		async checkNetworkConnection() {
			return new Promise((resolve) => {
				uni.getNetworkType({
					success: (res) => {
						const isConnected = res.networkType !== 'none'
						this.isOnline = isConnected
						resolve(isConnected)
					},
					fail: () => {
						this.isOnline = false
						resolve(false)
					}
				})
			})
		},

		// 带重试的API调用
		async callApiWithRetry(apiCall, maxRetries = 2) {
			let lastError = null

			for (let i = 0; i <= maxRetries; i++) {
				try {
					// 检查网络连接
					const isConnected = await this.checkNetworkConnection()
					if (!isConnected) {
						throw new Error('网络连接不可用')
					}

					// 执行API调用
					const result = await apiCall()
					return result
				} catch (error) {
					lastError = error
					console.error(`API调用失败，第${i + 1}次尝试:`, error)

					// 如果不是最后一次重试，等待一段时间后重试
					if (i < maxRetries) {
						await this.delay(1000 * (i + 1)) // 递增延迟
					}
				}
			}

			throw lastError
		},

		// 延迟函数
		delay(ms) {
			return new Promise(resolve => setTimeout(resolve, ms))
		},

		// 格式化时间
		formatTime(timestamp) {
			if (!timestamp) return ''
			const date = new Date(timestamp)
			const now = new Date()
			const diff = now - date

			// 如果是今天
			if (diff < 24 * 60 * 60 * 1000) {
				return date.toLocaleTimeString('zh-CN', {
					hour: '2-digit',
					minute: '2-digit'
				})
			}

			// 如果是昨天或更早
			return date.toLocaleDateString('zh-CN', {
				month: '2-digit',
				day: '2-digit',
				hour: '2-digit',
				minute: '2-digit'
			})
		},

		// 根据智能体配置更新页面信息
		updatePageInfo() {
			console.log('=== 开始更新页面信息 ===')
			console.log('当前主题数据:', this.currentTheme)
			console.log('当前主题类型:', typeof this.currentTheme)

			if (this.currentTheme) {
				console.log('✅ 有主题数据，开始更新页面信息')
				console.log('主题详细信息:', JSON.stringify(this.currentTheme, null, 2))

				// 更新智能体标题
				if (this.currentTheme.title) {
					console.log('更新标题:', this.currentTheme.title)
					this.agentTitle = this.currentTheme.title
					this.welcomeTitle = `${this.currentTheme.title}`
				} else {
					console.log('⚠️ 主题中没有title字段')
				}

				// 更新智能体描述
				if (this.currentTheme.description) {
					console.log('更新描述:', this.currentTheme.description)
					this.welcomeDesc = this.currentTheme.description
				} else {
					console.log('⚠️ 主题中没有description字段')
				}

				// 更新智能体头像
				if (this.currentTheme.icon) {
					console.log('更新头像:', this.currentTheme.icon)
					// 处理图片URL，如果是相对路径则添加图片服务器地址
					if (this.currentTheme.icon.startsWith('/')) {
						this.agentAvatar = IMAGE_BASE_URL + this.currentTheme.icon
					} else {
						this.agentAvatar = this.currentTheme.icon
					}
					console.log('最终头像URL:', this.agentAvatar)
				} else {
					console.log('⚠️ 主题中没有icon字段')
				}

				// 更新副标题（保持默认的消息提分信息）
				// 不显示类型信息，保持原有的副标题

				// 更新消息扣除点数
				if (this.currentTheme.consumption !== undefined && this.currentTheme.consumption !== null) {
					console.log('更新消息扣除点数:', this.currentTheme.consumption)
					this.consumption = this.currentTheme.consumption
				} else {
					console.log('⚠️ 主题中没有consumption字段，使用默认值1')
					this.consumption = 1
				}

				// 更新多媒体内容配置
				console.log('🔄 更新多媒体内容配置')
				this.updateMediaContents(this.currentTheme)

				console.log('✅ 页面信息更新完成:', {
					title: this.agentTitle,
					subtitle: this.agentSubtitle,
					avatar: this.agentAvatar,
					welcomeTitle: this.welcomeTitle,
					welcomeDesc: this.welcomeDesc,
					consumption: this.consumption,
					mediaContents: this.mediaContents
				})
			} else {
				console.log('❌ 没有主题数据，无法更新页面信息')
			}
			console.log('=== 页面信息更新结束 ===')
		},

		// 获取用户点数信息
		async fetchUserPoints() {
			try {
				console.log('🔄 开始获取用户点数信息')

				// 获取存储的token
				const token = uni.getStorageSync('token')
				console.log('获取到的token:', token ? '已获取' : '未获取')

				// 如果没有token，设置为0点数提示用户登录
				if (!token) {
					console.warn('⚠️ 未找到认证token，用户未登录')
					this.remainingPoints = 0 // 未登录用户点数为0
					return
				}

				// 调用后端API获取用户信息
				const response = await uni.request({
					url: `${API_BASE_URL}/api/members/me`,
					method: 'GET',
					header: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${token}`
					}
				})

				console.log('用户信息API响应:', response)

				if (response.statusCode === 200 && response.data) {
					// 处理会员API的响应格式
					if (response.data.success && response.data.data) {
						const userData = response.data.data
						console.log('✅ 获取到会员信息:', userData)

						// 更新剩余点数 - 会员系统使用points字段
						if (userData.points !== undefined) {
							this.remainingPoints = userData.points || 0
							console.log('✅ 更新用户剩余点数(points):', this.remainingPoints)
						} else if (userData.balance !== undefined) {
							this.remainingPoints = userData.balance || 0
							console.log('✅ 更新用户剩余点数(balance):', this.remainingPoints)
						} else {
							console.log('⚠️ 用户数据中没有找到点数字段，设置为0')
							this.remainingPoints = 0 // 数据异常时设置为0
						}
					} else {
						console.warn('⚠️ API响应格式异常:', response.data)
						this.remainingPoints = 0 // API响应异常时设置为0
					}
				} else {
					console.warn('⚠️ 获取用户信息失败，设置为0')
					this.remainingPoints = 0 // API调用失败时设置为0
				}
			} catch (error) {
				console.error('❌ 获取用户点数失败:', error)
				this.remainingPoints = 0 // 错误时设置为0
				// 不显示错误提示，避免影响用户体验
			}
		},

		// 检查用户登录状态
		checkUserLoginStatus() {
			try {
				console.log('🔄 检查用户登录状态')

				// 检查是否已登录
				this.isLoggedIn = userStore.isLoggedIn()
				this.userInfo = userStore.getCurrentUser()

				console.log('用户登录状态:', this.isLoggedIn)
				console.log('用户信息:', this.userInfo)

				if (this.isLoggedIn && this.userInfo) {
					console.log('✅ 用户已登录:', this.userInfo.username)
					// 如果已登录，获取最新的用户点数信息
					this.fetchUserPoints()
				} else {
					console.log('⚠️ 用户未登录，点数设置为0')
					// 未登录时点数为0，促使用户登录
					this.remainingPoints = 0
				}
			} catch (error) {
				console.error('❌ 检查用户登录状态失败:', error)
				this.isLoggedIn = false
				this.userInfo = null
				this.remainingPoints = 0 // 错误时设置为0
			}
		},

		// 显示登录界面
		showLoginModal() {
			// 这里可以跳转到登录页面或显示登录弹窗
			uni.navigateTo({
				url: '/pages/auth/login'
			})
		},

		// ========== 自定义弹窗相关方法 ==========

		// 显示点数不足弹窗
		showPointsInsufficientModal(requiredPoints) {
			console.log('🔔 显示自定义点数不足弹窗')
			this.requiredPointsForModal = requiredPoints
			this.showPointsModal = true
		},

		// 关闭点数不足弹窗
		closePointsModal() {
			console.log('❌ 关闭点数不足弹窗')
			this.showPointsModal = false
			this.requiredPointsForModal = 0
		},

		// 处理购买套餐按钮点击
		handleBuyPackage() {
			console.log('🛒 用户点击购买套餐')
			this.closePointsModal()
			this.navigateToMembershipPage()
		},

		// 跳转到会员套餐页面
		navigateToMembershipPage() {
			console.log('🛒 跳转到会员套餐页面')

			// 检查用户是否已登录
			const token = uni.getStorageSync('token')
			if (!token) {
				// 未登录，显示登录弹窗
				this.showLoginModalFlag = true
				return
			}

			// 已登录，跳转到套餐页面
			try {
				uni.navigateTo({
					url: '/pages/chat/index'
				})
			} catch (error) {
				console.error('跳转到套餐页面失败:', error)
				// 如果套餐页面不存在，可以跳转到其他相关页面
				uni.showToast({
					title: '页面暂未开放',
					icon: 'none'
				})
			}
		},

		// 显示错误提示
		showErrorToast(message, duration = 2000) {
			uni.showToast({
				title: message,
				icon: 'error',
				duration: duration
			})
		},

		// 显示成功提示
		showSuccessToast(message, duration = 1500) {
			uni.showToast({
				title: message,
				icon: 'success',
				duration: duration
			})
		},

		// 获取友好的错误消息
		getErrorMessage(error) {
			if (!error) return '未知错误'

			const message = error.message || error.toString()

			// 请求频率限制错误
			if (message.includes('429') || message.includes('频繁') || message.includes('过于频繁')) {
				return '请求过于频繁，请稍后再试'
			}

			// 网络相关错误
			if (message.includes('网络') || message.includes('network') || message.includes('timeout')) {
				return '网络连接异常，请检查网络设置'
			}

			// 认证相关错误
			if (message.includes('401') || message.includes('unauthorized') || message.includes('token')) {
				return '认证失败，请重新配置'
			}

			// 服务器错误
			if (message.includes('500') || message.includes('server') || message.includes('internal')) {
				return '服务器暂时不可用，请稍后重试'
			}

			// 配置错误
			if (message.includes('配置') || message.includes('config')) {
				return '配置信息有误，请检查设置'
			}

			// 智能体相关错误
			if (message.includes('bot') || message.includes('智能体')) {
				return '智能体服务异常，请稍后重试'
			}

			// 默认错误消息
			return '服务暂时不可用，请稍后重试'
		},

		// 视频加载完成事件
		onVideoLoaded(event) {
			console.log('视频loadedmetadata事件:', event)
			this.calculateVideoHeight(event)
		},

		// 视频可以播放时
		onVideoCanPlay(event) {
			console.log('视频canplay事件:', event)
			this.calculateVideoHeight(event)
		},

		// 视频时间更新时（作为备用方案）
		onVideoTimeUpdate(event) {
			// 只在第一次时间更新时计算，避免重复计算
			if (this.videoHeight === 250) {
				console.log('视频timeupdate事件:', event)
				this.calculateVideoHeight(event)
			}
		},

		// 计算视频高度的通用方法
		calculateVideoHeight(event) {
			console.log('计算视频高度，事件数据:', event)

			// 尝试从不同的事件属性中获取视频尺寸
			let videoWidth, videoHeight

			if (event && event.detail) {
				videoWidth = event.detail.videoWidth || event.detail.width
				videoHeight = event.detail.videoHeight || event.detail.height
			}

			console.log('获取到的视频尺寸:', { videoWidth, videoHeight })

			if (videoWidth && videoHeight && videoWidth > 0 && videoHeight > 0) {
				// 计算容器宽度（屏幕宽度减去边距）
				const systemInfo = uni.getSystemInfoSync()
				const containerWidth = systemInfo.windowWidth - 40 // 减去左右边距

				// 根据视频比例计算自适应高度
				const aspectRatio = videoHeight / videoWidth
				const calculatedHeight = containerWidth * aspectRatio

				// 只有计算出的高度合理时才更新
				if (calculatedHeight > 50 && calculatedHeight < 1000) {
					this.videoHeight = Math.round(calculatedHeight)

					console.log('✅ 视频尺寸调整成功:', {
						原始尺寸: `${videoWidth}x${videoHeight}`,
						屏幕宽度: systemInfo.windowWidth,
						容器宽度: containerWidth,
						宽高比: aspectRatio,
						计算高度: calculatedHeight,
						最终高度: this.videoHeight
					})

					// 强制更新视图
					this.$forceUpdate()
				} else {
					console.log('❌ 计算出的高度不合理:', calculatedHeight)
				}
			} else {
				console.log('❌ 无法获取视频尺寸，保持当前高度:', this.videoHeight)
			}
		},

		// 预加载视频信息
		preloadVideoInfo() {
			// 不再设置默认高度，让视频事件来处理
			// 只是确保视频组件能正确触发事件
			console.log('视频组件已准备就绪，等待视频事件触发高度计算')
		},

		// 视频错误处理
		onVideoError(event) {
			console.error('视频加载错误:', event)
			// 视频加载失败时，使用一个合理的默认高度
			const systemInfo = uni.getSystemInfoSync()
			const containerWidth = systemInfo.windowWidth - 40
			this.videoHeight = Math.round(containerWidth * 0.6) // 使用3:5的比例作为默认
			console.log('视频加载失败，使用默认高度:', this.videoHeight)
		},

		// 图片加载完成事件
		onImageLoaded(event) {
			console.log('图片加载完成:', event)
			// 这里可以根据图片的实际尺寸调整容器高度
			// 但由于uni-app的限制，我们保持固定高度以确保显示正常
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	width: 100%;
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	padding-bottom: 0; /* 移除底部导航栏空间 */
}



/* 主要内容区域 */
.main-content {
	flex: 1;
	padding: 5px 5px 100px; /* 增加底部padding，为悬浮按钮留出空间 */
	display: flex;
	flex-direction: column;
	overflow-y: auto;
	gap: 3px; /* 缩小整体间距 */
	align-items: center; /* 让内容居中 */
	justify-content: flex-start;
}

/* 加载状态 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 20px;
	gap: 16px;
}

.loading-spinner {
	width: 32px;
	height: 32px;
	border: 3px solid #f3f3f3;
	border-top: 3px solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 14px;
	color: #666;
}

/* 错误状态 */
.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 20px;
	gap: 16px;
}

.error-text {
	font-size: 14px;
	color: #ff4757;
	text-align: center;
}

.retry-btn {
	background-color: #007AFF;
	color: white;
	padding: 8px 16px;
	border-radius: 16px;
	font-size: 14px;
}

/* 欢迎容器 */
.welcome-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 12px;
	padding: 20px;
	width: 100%;
}

/* AI助手卡片 */
.ai-card {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 3px; /* 缩小头像与标题间距 */
	width: 100%;
	margin: 0 auto; /* 确保居中 */
	/* 移除背景、圆角和阴影，让头像单独显示 */

	.ai-avatar-container {
		width: 80px;
		height: 80px;
		border-radius: 20px;
		overflow: hidden;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

		.ai-avatar {
			width: 100%;
			height: 100%;
		}
	}

	/* 图片卡片样式 - 继承 media-card 的样式 */

	.image-container {
		position: relative;
		width: 100%;
		height: 200px;
		overflow: hidden;
		border-radius: 10px;
	}

	.media-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
		background: #f5f5f5;
	}

/* 确保视频也使用相同样式 */
video.media-image {
	width: 100%;
	height: 100%;
	object-fit: contain;
	background: #000;
	border-radius: 8px;
}



}

/* 欢迎消息 */
.welcome-message {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 3px; /* 减少上下间距 */
	text-align: center;
	width: 100%; /* 完全占满宽度 */
	max-width: 350px; /* 与媒体卡片保持一致的最大宽度 */
	margin: 3px auto; /* 减少上下外边距 */
	padding: 0; /* 无内边距 */
	box-sizing: border-box;

	.welcome-title {
		font-size: 16px;
		font-weight: 500;
		color: #000;
		line-height: 20px;
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		border: 1px solid #dee2e6;
		border-radius: 10px;
		padding: 10px 16px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
		text-align: center;
		display: block; /* 改为block确保居中 */
		width: auto; /* 根据内容自适应宽度 */
		margin: 0 auto; /* 确保居中显示 */
		box-sizing: border-box;
		min-height: auto; /* 高度自适应文字内容 */
	}

	.welcome-desc {
		font-size: 15px;
		color: #666;
		line-height: 22px;
		font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', 'Arial', sans-serif;
		font-style: italic;
		text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
		letter-spacing: 0.5px;
		background: rgba(255, 255, 255, 0.8);
		border: 1px solid rgba(0, 0, 0, 0.08);
		border-radius: 10px;
		padding: 10px 16px;
		box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
		text-align: center;
		display: block; /* 改为block，占满容器宽度 */
		width: 100%; /* 占满容器宽度，与媒体卡片一致 */
		box-sizing: border-box;
		backdrop-filter: blur(10px);
		min-height: auto; /* 高度自适应文字内容 */
	}

	.user-type {
		font-size: 14px;
		color: #999;
		margin-top: 8px;
	}
}

/* 聊天消息列表 */
.chat-messages {
	width: 100%;
	padding: 20px 0;
	display: flex;
	flex-direction: column;
	gap: 16px;
	margin-top: 10px;
}

.message-item {
	display: flex;
	gap: 12px;

	&.user-message {
		flex-direction: row-reverse;

		.message-content {
			align-items: flex-end;
		}

		.message-bubble {
			background-color: #007AFF;
			color: white;
		}
	}

	&.assistant-message {
		flex-direction: row;

		.message-content {
			align-items: flex-start;
		}

		.message-bubble {
			background-color: #f5f5f5;
			color: #333;
		}
	}

	&.error-message {
		.message-bubble {
			background-color: #ffe6e6;
			color: #ff4757;
			border: 1px solid #ffcccc;
		}
	}
}

.message-avatar {
	width: 36px;
	height: 36px;
	flex-shrink: 0;

	.avatar {
		width: 100%;
		height: 100%;
		border-radius: 18px;
	}
}

.message-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
	max-width: calc(100% - 60px);
}

.message-bubble {
	padding: 12px 16px;
	border-radius: 18px;
	max-width: 100%;
	word-wrap: break-word;

	&.typing {
		padding: 16px;
		background-color: #f5f5f5;
	}
}

.message-text {
	font-size: 16px;
	line-height: 22px;
	word-wrap: break-word;
	white-space: pre-wrap;
}

/* 图片消息样式 */
.image-message {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.message-image {
	max-width: 100%;
	max-height: 300px;
	border-radius: 8px;
	cursor: pointer;
}

.image-actions {
	display: flex;
	justify-content: flex-end;
}

/* URL消息样式 */
.url-message {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.url-text {
	font-size: 14px;
	color: #007AFF;
	word-wrap: break-word;
	cursor: pointer;
	text-decoration: underline;
}

.url-actions {
	display: flex;
	justify-content: flex-end;
}

/* 复制按钮样式 */
.copy-btn {
	font-size: 12px;
	color: #007AFF;
	padding: 4px 8px;
	border: 1px solid #007AFF;
	border-radius: 4px;
	cursor: pointer;
	background: transparent;
}

.copy-btn:hover {
	background: #007AFF;
	color: white;
}

/* 正在工作中的消息样式 */
.working-message {
	opacity: 0.9;
}

.working-bubble {
	background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
	border: 1px solid #b3d9ff;
}

.working-indicator {
	display: flex;
	align-items: center;
	gap: 8px;
}

.loading-dots {
	display: flex;
	gap: 4px;
}

.dot {
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background: #007AFF;
	animation: loading-bounce 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) {
	animation-delay: -0.32s;
}

.dot:nth-child(2) {
	animation-delay: -0.16s;
}

.working-text {
	font-size: 14px;
	color: #666;
	font-style: italic;
}

@keyframes loading-bounce {
	0%, 80%, 100% {
		transform: scale(0.8);
		opacity: 0.5;
	}
	40% {
		transform: scale(1);
		opacity: 1;
	}
}



.stop-text {
	font-size: 14px;
	font-weight: 500;
}

/* 开始聊天按钮样式 */
.start-chat-container {
	position: fixed;
	bottom: 20px;
	left: 50%;
	transform: translateX(-50%);
	z-index: 999;
	display: flex;
	align-items: center;
	gap: 24px;
	width: auto;
	justify-content: center;
}

/* 功能按钮样式 */
.function-btn {
	width: 50px;
	height: 50px;
	border-radius: 25px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.history-btn {
	background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
}

.share-btn {
	background: linear-gradient(135deg, #4ECDC4 0%, #6BCCC4 100%);
}

.function-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.btn-icon {
	font-size: 20px;
	color: white;
}

.start-chat-btn {
	display: flex;
	align-items: center;
	gap: 12px;
	background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
	color: white;
	padding: 16px 36px;
	border-radius: 30px;
	box-shadow: 0 8px 20px rgba(0, 122, 255, 0.3);
	cursor: pointer;
	transition: all 0.3s ease;
	min-width: 120px;
	white-space: nowrap;
	justify-content: center;
}

.start-chat-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 12px 25px rgba(0, 122, 255, 0.4);
}

.chat-icon {
	font-size: 20px;
}

.chat-text {
	font-size: 16px;
	font-weight: 600;
}

/* 聊天弹窗样式 */
.chat-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
	padding: 20px;
}

.chat-modal {
	background: white;
	border-radius: 20px;
	width: 90%;
	max-width: 500px;
	max-height: 85vh;
	height: 600px;
	display: flex;
	flex-direction: column;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
	overflow: hidden;
}

/* 弹窗头部 */
.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20px;
	background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
	color: white;
}

.header-info {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 8px;
}

.modal-title {
	font-size: 18px;
	font-weight: 600;
	color: white;
}

.points-info {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.points-text {
	font-size: 14px;
	color: rgba(255, 255, 255, 0.9);
	font-weight: 500;
}

.consumption-text {
	font-size: 14px;
	color: rgba(255, 255, 255, 0.9);
	font-weight: 500;
}

.close-btn {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.close-btn:hover {
	background: rgba(255, 255, 255, 0.3);
}

.close-icon {
	font-size: 16px;
	font-weight: bold;
}



/* 弹窗聊天区域 */
.modal-chat-container {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.welcome-message {
	display: flex;
	align-items: flex-start;
	gap: 12px;
	padding: 20px;
	border-bottom: 1px solid #f0f0f0;
}

.welcome-avatar {
	flex-shrink: 0;
}

.welcome-avatar .avatar {
	width: 36px;
	height: 36px;
	border-radius: 50%;
}

.welcome-content {
	flex: 1;
}

.welcome-text {
	font-size: 14px;
	color: #666;
	line-height: 1.5;
}

.modal-messages-container {
	flex: 1;
	overflow-y: auto;
	padding: 15px 20px;
	max-height: 500px;
	min-height: 400px;
}

.modal-message-item {
	display: flex;
	margin-bottom: 16px;
	align-items: flex-start;
	gap: 12px;
}

.modal-message-item.user-message {
	flex-direction: row-reverse;
}

.modal-message-avatar {
	flex-shrink: 0;
}

.modal-message-avatar .avatar {
	width: 32px;
	height: 32px;
	border-radius: 50%;
}

.modal-message-content {
	flex: 1;
	max-width: 70%;
	display: flex;
	flex-direction: column;
}

.modal-message-bubble {
	padding: 12px 16px;
	border-radius: 18px;
	word-wrap: break-word;
	position: relative;
	display: inline-block;
	max-width: 100%;
	width: fit-content;
	min-width: 60px;
}

.modal-message-item.user-message .modal-message-content {
	align-items: flex-end;
}

.modal-message-item.user-message .modal-message-bubble {
	background: #007AFF;
	color: white;
}

.modal-message-item.assistant-message .modal-message-content {
	align-items: flex-start;
}

.modal-message-item.assistant-message .modal-message-bubble {
	background: #f0f0f0;
	color: #333;
}

.modal-message-text {
	font-size: 14px;
	line-height: 1.4;
}

/* 上传中的消息样式 */
.uploading-bubble {
	opacity: 0.7;
	background: #e0e0e0 !important;
	color: #666 !important;
}

/* 图片消息样式 */
.image-message {
	display: flex;
	flex-direction: column;
	gap: 8px;
	max-width: 200px;
}

.message-image {
	width: 100%;
	max-width: 200px;
	border-radius: 8px;
	cursor: pointer;
}

.image-actions {
	display: flex;
	justify-content: flex-end;
}

/* 文件消息样式 */
.file-message {
	display: flex;
	flex-direction: column;
	gap: 8px;
	min-width: 200px;
}

.file-info {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 8px;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 8px;
}

.file-icon {
	font-size: 24px;
}

.file-details {
	display: flex;
	flex-direction: column;
	gap: 4px;
	flex: 1;
}

.file-name {
	font-size: 14px;
	font-weight: 500;
	color: inherit;
}

.file-url {
	font-size: 12px;
	color: rgba(255, 255, 255, 0.8);
	cursor: pointer;
	text-decoration: underline;
}

.user-bubble .file-url {
	color: rgba(255, 255, 255, 0.8);
}

.assistant-bubble .file-url {
	color: #666;
}



/* 弹窗输入区域 */
.modal-input-container {
	border-top: 1px solid #f0f0f0;
	padding: 16px 20px;
	background: #fafafa;
}

.modal-input-wrapper {
	display: flex;
	align-items: center;
	gap: 8px;
	background: white;
	border-radius: 25px;
	padding: 8px 16px;
	border: 1px solid #e0e0e0;
}

.modal-add-btn {
	width: 32px;
	height: 32px;
	border-radius: 16px;
	background: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	flex-shrink: 0;
}

.modal-add-btn:hover {
	background: #e0e0e0;
}

.add-icon {
	font-size: 18px;
	color: #666;
	font-weight: bold;
}

/* 附件选择菜单 */
.attachment-menu-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.3);
	display: flex;
	align-items: flex-end;
	justify-content: center;
	z-index: 10000;
	padding: 20px;
}

.attachment-menu {
	background: white;
	border-radius: 15px 15px 0 0;
	width: 100%;
	max-width: 500px;
	padding: 0;
	animation: slideUp 0.3s ease-out;
	box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

/* 菜单头部 */
.menu-header {
	padding: 20px 20px 10px 20px;
	border-bottom: 1px solid #f0f0f0;
}

.menu-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
	text-align: center;
}

.attachment-option {
	display: flex;
	align-items: center;
	padding: 18px 20px;
	cursor: pointer;
	transition: background-color 0.3s ease;
	gap: 15px;
	border-bottom: 1px solid #f8f8f8;
}

.attachment-option:hover {
	background: #f5f5f5;
}

.attachment-option:last-child {
	border-bottom: none;
}

.attachment-option.cancel-option {
	border-top: 8px solid #f0f0f0;
	justify-content: center;
	color: #666;
	margin-top: 0;
}

.attachment-option.disabled-option {
	opacity: 0.6;
	cursor: not-allowed;
}

.attachment-option.disabled-option:hover {
	background: transparent;
}

.option-icon {
	font-size: 28px;
	width: 35px;
	text-align: center;
	flex-shrink: 0;
}

.option-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.option-text {
	font-size: 16px;
	color: #333;
	font-weight: 500;
}

.option-desc {
	font-size: 13px;
	color: #999;
}

.cancel-option .option-text {
	color: #666;
	font-weight: normal;
}

.disabled-option .option-text {
	color: #999;
}

.disabled-option .option-desc {
	color: #ccc;
}

.modal-message-input {
	flex: 1;
	border: none;
	outline: none;
	font-size: 14px;
	padding: 8px 0;
}

.modal-send-btn {
	background: #007AFF;
	color: white;
	padding: 8px 16px;
	border-radius: 18px;
	font-size: 14px;
	cursor: pointer;
	transition: all 0.3s ease;
	white-space: nowrap;
}

.modal-send-btn.disabled {
	background: #ccc;
	cursor: not-allowed;
}

.modal-send-btn:not(.disabled):hover {
	background: #0056b3;
}

.modal-stop-btn {
	background: #ff4757;
	color: white;
	padding: 8px 12px;
	border-radius: 18px;
	font-size: 14px;
	cursor: pointer;
	transition: all 0.3s ease;
	white-space: nowrap;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-stop-btn:hover {
	background: #ff3742;
}

.modal-stop-btn .stop-icon {
	font-size: 16px;
	font-weight: bold;
}

.message-time {
	font-size: 12px;
	color: #999;
	margin: 0 8px;
}

/* 打字指示器 */
.typing-indicator {
	display: flex;
	gap: 4px;
	align-items: center;
}

.typing-dot {
	width: 6px;
	height: 6px;
	background-color: #999;
	border-radius: 50%;
	animation: typing 1.4s infinite ease-in-out;

	&:nth-child(1) {
		animation-delay: -0.32s;
	}

	&:nth-child(2) {
		animation-delay: -0.16s;
	}
}

@keyframes typing {
	0%, 80%, 100% {
		transform: scale(0.8);
		opacity: 0.5;
	}
	40% {
		transform: scale(1);
		opacity: 1;
	}
}



/* 底部输入区域 */
.input-area {
	position: fixed;
	bottom: 20px;
	left: 20px;
	right: 20px;
	z-index: 100;

	.input-container {
		display: flex;
		align-items: center;

		.input-wrapper {
			flex: 1;
			background-color: #f8f8f8;
			border-radius: 20px;
			border: none !important;
			outline: none !important;
			box-shadow: none !important;
			display: flex;
			align-items: center;
			padding: 0 4px;
			height: 36px;
		}

			.add-btn {
				width: 28px;
				height: 28px;
				display: flex;
				align-items: center;
				justify-content: center;
				border: none !important;
				outline: none !important;
				box-shadow: none !important;

				.add-icon {
					font-size: 20px;
					color: #666;
				}
			}

			.message-input {
				flex: 1;
				border: none !important;
				border-width: 0 !important;
				border-style: none !important;
				border-color: transparent !important;
				outline: none !important;
				outline-width: 0 !important;
				outline-style: none !important;
				outline-color: transparent !important;
				background: transparent;
				font-size: 16px;
				padding: 0 8px;
				height: 100%;
				box-shadow: none !important;
				-webkit-box-shadow: none !important;
				-moz-box-shadow: none !important;
				-webkit-appearance: none !important;
				-moz-appearance: none !important;
				appearance: none !important;
				-webkit-tap-highlight-color: transparent !important;
				-webkit-focus-ring-color: transparent !important;
			}

			.message-input:focus {
				border: none !important;
				border-width: 0 !important;
				outline: none !important;
				outline-width: 0 !important;
				box-shadow: none !important;
				-webkit-box-shadow: none !important;
				-moz-box-shadow: none !important;
			}

			.message-input:active {
				border: none !important;
				outline: none !important;
				box-shadow: none !important;
			}

			.message-input::before,
			.message-input::after {
				display: none !important;
			}

			.send-btn {
				min-width: 60px;
				height: 28px;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #f0f0f0;
				border-radius: 14px;
				border: none !important;
				outline: none !important;
				box-shadow: none !important;
				transition: all 0.3s ease;

				&.active {
					background-color: #007AFF;
					color: white;
				}

				.send-icon {
					font-size: 14px;
					font-weight: 500;
				}

				.sending-icon {
					display: flex;
					align-items: center;
					justify-content: center;

					.spinner {
						width: 16px;
						height: 16px;
						border: 2px solid #f3f3f3;
						border-top: 2px solid #007AFF;
						border-radius: 50%;
						animation: spin 1s linear infinite;
					}
				}
			}
		}
	}

/* 多媒体内容卡片 */
.media-cards {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 3px; /* 进一步减少卡片之间的间距 */
	margin: 0 auto; /* 确保居中 */
	width: 100%; /* 完全占满宽度 */
	max-width: 350px; /* 与其他卡片保持一致 */
	padding: 0; /* 无内边距 */
	box-sizing: border-box;
}

.media-card {
	background: rgba(255, 255, 255, 0.9) !important;
	border-radius: 12px !important;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
	border: 3px solid #c0c0c0 !important;
	overflow: hidden !important;
	transition: all 0.3s ease;
	width: 100% !important;
	max-width: 350px !important; /* 减小最大宽度，保持与其他卡片一致 */
	position: relative;
	padding: 12px !important;
	margin: 0 auto !important;
}

/* 更具体的选择器确保样式应用 */
.media-cards .media-section .media-card {
	background: rgba(255, 255, 255, 0.9) !important;
	border: 3px solid #c0c0c0 !important;
	border-radius: 12px !important;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
	padding: 12px !important;
}

.media-cards .media-section .video-card {
	border: 3px solid #c0c0c0 !important;
}

.media-cards .media-section .image-card {
	border: 3px solid #c0c0c0 !important;
}

.media-cards .media-section .text-card {
	border: 3px solid #c0c0c0 !important;
}





.media-card:active {
	transform: scale(0.98);
	box-shadow: 0 1px 8px rgba(0, 0, 0, 0.12);
}

/* 媒体区域样式 */
.media-section {
	margin-bottom: 0;
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.media-header {
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	flex-direction: row !important;
	padding: 10px 16px;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-bottom: 1px solid #f0f0f0;
	gap: 3px;
	margin-bottom: 3px;
	border-radius: 12px;
}

.media-type-icon {
	width: 24px;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.type-icon {
	font-size: 16px;
}

.media-title {
	font-size: 15px;
	font-weight: 500;
	color: #333;
}

/* 视频卡片样式 */
.video-card .media-header {
	margin-bottom: 3px;
}

.image-card .media-header {
	margin-bottom: 3px;
}

.text-card .media-header {
	margin-bottom: 3px;
}

/* 图片和视频容器样式 */
.image-container {
	position: relative;
	width: 100%;
	height: auto;
	overflow: hidden;
	border-radius: 10px;
	background: #f5f5f5;
}

.media-image {
	width: 100%;
	height: auto;
	border-radius: 8px;
	display: block;
}

/* 文字卡片样式 */
.text-card {
	min-height: 120px;
	display: flex;
	flex-direction: column;
	/* 其他样式继承 media-card */
}

/* 文字内容容器 */
.text-content-wrapper {
	position: relative;
	width: 100%;
	min-height: auto;
	padding: 0;
	background: rgba(248, 249, 250, 0.8);
	border-radius: 10px;
	margin-top: 3px;
	overflow: visible;
}

/* 通用弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 20px 0 20px;
	border-bottom: 1px solid #f0f0f0;
	margin-bottom: 20px;
}

.modal-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.close-btn {
	width: 30px;
	height: 30px;
	border-radius: 15px;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 16px;
	color: #666;
	transition: all 0.3s ease;
}

.close-btn:hover {
	background: #e0e0e0;
}

/* 历史记录弹窗样式 */
.history-modal {
	width: 90%;
	max-width: 500px;
	max-height: 80vh;
	background: white;
	border-radius: 12px;
	overflow: hidden;
}

.history-content {
	max-height: 60vh;
	overflow-y: auto;
	padding: 0 20px 20px 20px;
}

.loading-container, .empty-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40px 20px;
}

.loading-text, .empty-text {
	color: #999;
	font-size: 14px;
}

.history-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.history-item {
	padding: 16px;
	background: #f8f9fa;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
	position: relative;
}

.history-item:hover {
	background: #e9ecef;
	transform: translateY(-1px);
}

/* 复制按钮样式 */
.copy-btn {
	position: absolute;
	top: 12px;
	right: 12px;
	width: 32px;
	height: 32px;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 6px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.copy-btn:hover {
	background: rgba(255, 255, 255, 1);
	transform: scale(1.1);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.copy-icon {
	font-size: 14px;
	color: #666;
}

.history-time {
	font-size: 12px;
	color: #999;
	margin-bottom: 8px;
}

.history-preview {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.user-message, .ai-message {
	font-size: 14px;
	line-height: 1.4;
}

.user-message {
	color: #333;
}

.ai-message {
	color: #666;
}

.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 16px;
	cursor: pointer;
	color: #007AFF;
	font-size: 14px;
}

.load-more:hover {
	background: #f8f9fa;
	border-radius: 8px;
}

/* 分享弹窗样式 */
.share-modal {
	width: 90%;
	max-width: 400px;
	background: white;
	border-radius: 12px;
	overflow: hidden;
}

.share-content {
	padding: 0 20px 20px 20px;
}

.share-info {
	margin-bottom: 24px;
}

.agent-info {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 16px;
	background: #f8f9fa;
	border-radius: 8px;
}

.agent-avatar {
	width: 50px;
	height: 50px;
	border-radius: 25px;
	background: #e0e0e0;
}

.agent-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.agent-name {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.agent-desc {
	font-size: 14px;
	color: #666;
	line-height: 1.4;
}

.share-methods {
	display: flex;
	flex-direction: column;
	gap: 12px;
	margin-bottom: 20px;
}

.share-method {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 16px;
	background: #f8f9fa;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.share-method:hover {
	background: #e9ecef;
	transform: translateY(-1px);
}

.method-icon {
	font-size: 20px;
}

.method-text {
	font-size: 16px;
	color: #333;
}

.share-code-container {
	padding: 16px;
	background: #f8f9fa;
	border-radius: 8px;
	border: 1px dashed #ddd;
}

.share-code-label {
	font-size: 14px;
	color: #666;
	margin-bottom: 8px;
}

.share-code-box {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 12px;
}

.share-code {
	flex: 1;
	font-size: 16px;
	font-weight: 600;
	color: #007AFF;
	font-family: monospace;
}

.copy-code-btn {
	padding: 6px 12px;
	background: #007AFF;
	color: white;
	border-radius: 4px;
	font-size: 12px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.copy-code-btn:hover {
	background: #0056CC;
}

/* 文字教学内容 - 清晰易读的字体样式 */
.text-teaching-content {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', sans-serif;
	font-size: 14px;
	color: #333;
	line-height: 1.6;
	font-weight: 400;
	word-wrap: break-word;
	display: block;
	width: 100%;
	min-height: auto;
	padding: 16px;
	box-sizing: border-box;
	background: rgba(255, 255, 255, 0.8);
	border: 1px solid rgba(0, 0, 0, 0.08);
	border-radius: 10px;
	box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
}

.media-description {
	padding: 8px 16px 12px;
	border-top: 1px solid #f8f8f8;
}

.description-text {
	font-size: 12px;
	color: #999;
	line-height: 16px;
	font-style: italic;
}

/* 自定义点数不足弹窗样式 */
.points-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	padding: 20px;
}

.points-modal {
	background: white;
	border-radius: 20px;
	width: 90%;
	max-width: 400px;
	overflow: hidden;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
	animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
	from {
		opacity: 0;
		transform: scale(0.8) translateY(-20px);
	}
	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

.points-modal-header {
	background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
	color: white;
	padding: 20px;
	display: flex;
	align-items: center;
	position: relative;
}

.points-icon {
	font-size: 24px;
	margin-right: 12px;
}

.points-title {
	font-size: 20px;
	font-weight: bold;
	flex: 1;
}

.points-close-btn {
	width: 32px;
	height: 32px;
	border-radius: 16px;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.2s ease;
}

.points-close-btn:hover {
	background: rgba(255, 255, 255, 0.3);
}

.points-close-icon {
	font-size: 16px;
	font-weight: bold;
}

.points-modal-content {
	padding: 24px;
}

.points-info-card {
	background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
	border-radius: 16px;
	padding: 20px;
	margin-bottom: 20px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.points-current, .points-required {
	text-align: center;
	flex: 1;
}

.points-label {
	font-size: 14px;
	color: #666;
	display: block;
	margin-bottom: 8px;
}

.points-value {
	font-size: 28px;
	font-weight: bold;
	color: #2ed573;
	display: block;
	margin-bottom: 4px;
}

.points-value.required {
	color: #ff6b6b;
}

.points-unit {
	font-size: 14px;
	color: #999;
}

.points-divider {
	width: 2px;
	height: 60px;
	background: linear-gradient(to bottom, transparent, #ddd, transparent);
	margin: 0 20px;
}

.points-message {
	text-align: center;
	line-height: 1.6;
}

.message-text {
	font-size: 16px;
	color: #333;
	line-height: 1.5;
	display: block;
	margin-bottom: 8px;
}

.message-sub {
	font-size: 14px;
	color: #666;
	display: block;
}

.points-modal-actions {
	padding: 0 24px 24px;
	display: flex;
	gap: 12px;
}

.points-btn {
	flex: 1;
	height: 48px;
	border-radius: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.2s ease;
}

.points-btn-cancel {
	background: #f5f5f5;
	color: #666;
}

.points-btn-cancel:hover {
	background: #e8e8e8;
}

.points-btn-primary {
	background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
	color: white;
	box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.points-btn-primary:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 16px rgba(0, 122, 255, 0.4);
}

.points-btn-text {
	font-size: 16px;
	font-weight: 500;
}


</style>