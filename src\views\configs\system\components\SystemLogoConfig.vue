<template>
  <div class="system-logo-config">
    <n-form
      ref="formRef"
      :model="formData"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
      :style="{
        maxWidth: '640px'
      }"
    >
      <!-- 收起时侧边栏Logo(迷你Logo) -->
      <n-form-item label="侧边栏迷你Logo" path="miniLogo">
        <div class="logo-upload-container">
          <div class="preview-wrapper preview-mini">
            <img v-if="formData.miniLogo" :src="formData.miniLogo" class="logo-preview" alt="迷你Logo预览" />
            <div v-else class="empty-logo">
              <n-icon size="24"><image-outline /></n-icon>
              <span>暂无Logo</span>
            </div>
          </div>
          <div class="logo-actions">
            <n-button type="primary" @click="openMediaSelector('miniLogo')">
              选择图片
            </n-button>
            <n-button v-if="formData.miniLogo" @click="clearLogo('miniLogo')">
              清除
            </n-button>
          </div>
        </div>
        <template #help>
          侧边栏Logo，建议尺寸：30×30px，正方形
        </template>
      </n-form-item>
      
      <!-- 用户信息Logo -->
      <n-form-item label="用户信息Logo" path="userLogo">
        <div class="logo-upload-container">
          <div class="preview-wrapper preview-mini">
            <img v-if="formData.userLogo" :src="formData.userLogo" class="logo-preview" alt="用户Logo预览" />
            <div v-else class="empty-logo">
              <n-icon size="24"><image-outline /></n-icon>
              <span>暂无Logo</span>
            </div>
          </div>
          <div class="logo-actions">
            <n-button type="primary" @click="openMediaSelector('userLogo')">
              选择图片
            </n-button>
            <n-button v-if="formData.userLogo" @click="clearLogo('userLogo')">
              清除
            </n-button>
          </div>
        </div>
        <template #help>
          顶部导航栏用户信息显示的Logo，建议尺寸：40×40px，正方形
        </template>
      </n-form-item>
      
      <!-- Favicon图标 -->
      <n-form-item label="网站图标(Favicon)" path="favicon">
        <div class="logo-upload-container">
          <div class="preview-wrapper preview-favicon">
            <img v-if="formData.favicon" :src="formData.favicon" class="logo-preview" alt="Favicon预览" />
            <div v-else class="empty-logo">
              <n-icon size="24"><image-outline /></n-icon>
              <span>暂无图标</span>
            </div>
          </div>
          <div class="logo-actions">
            <n-button type="primary" @click="openMediaSelector('favicon')">
              选择图片
            </n-button>
            <n-button v-if="formData.favicon" @click="clearLogo('favicon')">
              清除
            </n-button>
          </div>
        </div>
        <template #help>
          浏览器标签显示的网站图标，建议尺寸：32×32px或16×16px，支持.ico格式
        </template>
      </n-form-item>

      <!-- 客服图片 -->
      <n-form-item label="客服图片" path="customerServiceImage">
        <div class="logo-upload-container">
          <div class="preview-wrapper preview-customer-service">
            <img v-if="formData.customerServiceImage" :src="formData.customerServiceImage" class="logo-preview" alt="客服图片预览" />
            <div v-else class="empty-logo">
              <n-icon size="24"><image-outline /></n-icon>
              <span>暂无客服图片</span>
            </div>
          </div>
          <div class="logo-actions">
            <n-button type="primary" @click="openMediaSelector('customerServiceImage')">
              选择图片
            </n-button>
            <n-button v-if="formData.customerServiceImage" @click="clearLogo('customerServiceImage')">
              清除
            </n-button>
          </div>
        </div>
        <template #help>
          客服联系方式图片，可以是微信二维码、客服头像等，建议尺寸：200×200px
        </template>
      </n-form-item>
      
      <!-- 主题色 -->
      <n-form-item label="系统主题色" path="primaryColor">
        <n-color-picker v-model:value="formData.primaryColor" @update:value="handleColorChange" />
        <template #help>
          系统主要按钮、高亮等元素的颜色
        </template>
      </n-form-item>
      
      <div class="form-actions">
        <n-button type="primary" @click="saveConfig" :loading="saving">
          保存设置
        </n-button>
        <n-button @click="resetForm" :loading="resetting">
          重置表单
        </n-button>
      </div>
    </n-form>
    
    <!-- 媒体选择器模态框 -->
    <media-selector-modal
      v-model:show="showMediaSelector"
      type="image"
      @select="handleMediaSelected"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { NForm, NFormItem, NButton, NIcon, NColorPicker, useMessage } from 'naive-ui';
import { ImageOutline } from '@vicons/ionicons5';
import configApi from '@/api/config';
import MediaSelectorModal from '@/components/MediaSelectorModal.vue';

const emit = defineEmits(['saved']);
const message = useMessage();
const formRef = ref(null);
const saving = ref(false);
const resetting = ref(false);
const configType = 'system-logo';

// 媒体选择器
const showMediaSelector = ref(false);
const currentField = ref(null);

// 表单数据
const formData = reactive({
  mainLogo: '',
  sidebarLogo: '',
  miniLogo: '',
  userLogo: '',
  favicon: '',
  customerServiceImage: '',
  primaryColor: '#1890ff'
});

// 打开媒体选择器
function openMediaSelector(field) {
  currentField.value = field;
  showMediaSelector.value = true;
}

// 处理媒体选择
function handleMediaSelected(media) {
  if (currentField.value) {
    formData[currentField.value] = media.url;
    message.success('图片选择成功');
    
    // 如果是favicon，立即应用
    if (currentField.value === 'favicon') {
      applyFavicon(media.url);
    }
    
    // 如果是主题色，立即应用
    if (currentField.value === 'primaryColor') {
      applyThemeColor(formData.primaryColor);
    }
  }
}

// 应用网站图标
function applyFavicon(url) {
  const faviconLink = document.querySelector('link[rel="icon"]') || document.createElement('link');
  faviconLink.type = 'image/x-icon';
  faviconLink.rel = 'icon';
  faviconLink.href = url;
  document.head.appendChild(faviconLink);
}

// 应用主题色
function applyThemeColor(color) {
  document.documentElement.style.setProperty('--primary-color', color);
  
  // 提取RGB值用于透明度变化
  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };
  
  const rgb = hexToRgb(color);
  if (rgb) {
    const rgbValue = `${rgb.r}, ${rgb.g}, ${rgb.b}`;
    document.documentElement.style.setProperty('--primary-color-rgb', rgbValue);
  }
}

// 清除Logo
function clearLogo(field) {
  formData[field] = '';
}

// 处理颜色变化
function handleColorChange(value) {
  console.log('颜色变化:', value);
  formData.primaryColor = value;
  applyThemeColor(value);
}

// 保存配置
async function saveConfig() {
  saving.value = true;
  try {
    // 构建配置对象数据
    const configData = {
      miniLogo: formData.miniLogo || '',
      userLogo: formData.userLogo || '',
      favicon: formData.favicon || '',
      customerServiceImage: formData.customerServiceImage || '',
      primaryColor: formData.primaryColor || '#1890ff'
    };
    
    console.log('准备保存Logo配置:', configData);
    
    try {
      // 使用configApi.saveConfig方法保存配置
      const res = await configApi.saveConfig(configType, configData);
      
      console.log('Logo配置保存结果:', res);
      
      if (res && (res.success || res.code === 200)) {
        message.success('Logo和品牌配置保存成功');
        
        // 立即应用配置
        // 应用主题色
        applyThemeColor(formData.primaryColor);
        
        // 更新网站图标
        if (formData.favicon) {
          applyFavicon(formData.favicon);
        }
        
        // 强制更新组件
        const tempValue = formData.primaryColor;
        formData.primaryColor = '';
        setTimeout(() => {
          formData.primaryColor = tempValue;
        }, 10);
        
        // 通知父组件保存成功
        emit('saved', 'Logo和品牌');
        
        // 触发全局事件，通知其他组件配置已更新
        window.dispatchEvent(new CustomEvent('system-config-updated', { 
          detail: { type: configType, data: configData } 
        }));
        
        // 重新加载配置
        setTimeout(() => {
          loadConfig();
        }, 500);
      } else {
        message.error('保存配置失败: ' + (res?.message || '未知错误'));
        console.error('保存失败响应:', res);
      }
    } catch (apiError) {
      console.error('API调用异常:', apiError);
      if (apiError.response) {
        console.error('错误响应:', apiError.response);
        message.error(`保存失败: ${apiError.response.status} - ${apiError.response.data?.message || '未知错误'}`);
      } else if (apiError.request) {
        console.error('无响应:', apiError.request);
        message.error('保存失败: 服务器未响应，请检查网络连接和服务器端口');
      } else {
        console.error('请求配置错误:', apiError.message);
        message.error('保存失败: ' + apiError.message);
      }
    }
  } catch (error) {
    console.error('保存Logo配置失败:', error);
    message.error('保存失败: ' + (error.message || '未知错误'));
  } finally {
    saving.value = false;
  }
}

// 重置表单
function resetForm() {
  if (!window.confirm('确定要重置表单吗？这将清空当前所有修改。')) {
    return;
  }
  
  resetting.value = true;
  try {
    loadConfig();
  } finally {
    resetting.value = false;
  }
}

// 加载配置
async function loadConfig() {
  try {
    console.log('开始加载Logo配置...');
    
    // 使用configApi加载配置
    const res = await configApi.getConfig(configType);
    
    console.log('Logo配置加载结果:', res);
    
    if (res && (res.success || res.code === 200) && res.data) {
      const configValue = res.data.configValue || {};
      
      console.log('解析的配置数据:', configValue);
      
      // 更新表单数据
      formData.miniLogo = configValue.miniLogo || '';
      formData.userLogo = configValue.userLogo || '';
      formData.favicon = configValue.favicon || '';
      formData.customerServiceImage = configValue.customerServiceImage || '';
      formData.primaryColor = configValue.primaryColor || '#1890ff';
      
      console.log('表单数据已更新:', formData);
      
      // 应用主题色
      applyThemeColor(formData.primaryColor);
      
      // 更新网站图标
      if (formData.favicon) {
        applyFavicon(formData.favicon);
      }
      
      // 触发更新事件
      window.dispatchEvent(new CustomEvent('logo-config-loaded', {
        detail: { data: formData }
      }));
    } else {
      console.error('加载Logo配置失败:', res);
    }
  } catch (error) {
    console.error('加载Logo配置失败:', error);
    message.error('加载配置失败: ' + (error.message || '未知错误'));
  }
}

onMounted(() => {
  loadConfig();
});

// 暴露方法给父组件
defineExpose({
  loadConfig
});
</script>

<style scoped>
.system-logo-config {
  padding: 20px 0;
}

.logo-upload-container {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.preview-wrapper {
  width: 200px;
  height: 60px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #fafafa;
}

.preview-mini, .preview-favicon {
  width: 60px;
  height: 60px;
}

.preview-customer-service {
  width: 120px;
  height: 120px;
}

.logo-preview {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.empty-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 12px;
}

.logo-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  gap: 12px;
}
</style> 