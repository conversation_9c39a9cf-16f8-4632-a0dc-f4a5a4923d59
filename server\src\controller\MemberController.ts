import { Request, Response } from 'express';
import { AppDataSource } from '../data-source';
import { Member } from '../entity/Member';
import { Package } from '../entity/Package';
import { Config } from '../entity/Config';
import { Task } from '../entity/Task';
import { AgentTheme } from '../entity/AgentTheme';
import * as bcrypt from 'bcrypt';
import * as jwt from 'jsonwebtoken';
import { Between, Like } from 'typeorm';

export class MemberController {
    private memberRepository = AppDataSource.getRepository(Member);
    private packageRepository = AppDataSource.getRepository(Package);
    private configRepository = AppDataSource.getRepository(Config);

    // 获取合伙人配置
    private async getPartnerConfig() {
        try {
            const configs = await this.configRepository.find({
                where: { type: 'referral' }
            });

            const configData: { [key: string]: string } = {};
            configs.forEach(config => {
                configData[config.key] = config.value;
            });

            return {
                minWithdrawal: parseFloat(configData.min_withdrawal || '10'),
                withdrawalFee: parseFloat(configData.withdrawal_fee || '0'),
                withdrawalChannel: configData.withdrawal_channel || 'alipay'
            };
        } catch (error) {
            console.error('获取合伙人配置失败:', error);
            // 返回默认配置
            return {
                minWithdrawal: 10,
                withdrawalFee: 0,
                withdrawalChannel: 'alipay'
            };
        }
    }

    // 获取所有会员
    async all(request: Request, response: Response) {
        try {
            // 获取会员基本信息，不加载关联
            const members = await this.memberRepository
                .createQueryBuilder('member')
                .leftJoinAndSelect('member.package', 'package')
                .select([
                    'member.id',
                    'member.name',
                    'member.phone',
                    'member.email',
                    'member.accountType',
                    'member.packageId',
                    'member.points',
                    'member.balance',
                    'member.isPartner',
                    'member.referrerId',
                    'member.status',
                    'member.avatar',
                    'member.createdAt',
                    'member.updatedAt',
                    'package.id',
                    'package.title',
                    'package.price'
                ])
                .orderBy('member.id', 'DESC')
                .getMany();
            
            // 手动映射数据，处理余额格式
            const simplifiedMembers = members.map(member => {
                // 确保余额为数值类型
                let balance = 0;
                if (member.balance !== null && member.balance !== undefined) {
                    balance = Number(member.balance);
                    if (isNaN(balance)) balance = 0;
                }
                
                // 创建一个简单对象，只包含需要的属性
                return {
                    id: member.id,
                    name: member.name || '',
                    phone: member.phone || '',
                    email: member.email || '',
                    accountType: member.accountType || 'account',
                    packageId: member.packageId,
                    packageInfo: member.package ? {
                        id: member.package.id,
                        title: member.package.title,
                        price: member.package.price
                    } : null,
                    points: member.points || 0,
                    balance: balance,
                    isPartner: member.isPartner || false,
                    referrerId: member.referrerId,
                    status: member.status !== undefined ? member.status : true,
                    avatar: member.avatar || '',
                    createdAt: member.createdAt,
                    updatedAt: member.updatedAt
                };
            });
            
            // 直接发送响应，不使用内置的json方法
            response.setHeader('Content-Type', 'application/json; charset=utf-8');
            response.end(JSON.stringify(simplifiedMembers));
            return;
        } catch (error) {
            console.error('获取会员列表失败:', error);
            response.status(500).send(JSON.stringify({ message: '获取会员列表失败' }));
            return;
        }
    }

    // 获取会员详情
    async one(request: Request, response: Response) {
        try {
            const idParam = request.params.id;

            // 验证ID参数
            if (!idParam || idParam === 'undefined' || idParam === 'null') {
                return response.status(400).json({ message: '无效的会员ID' });
            }

            const id = parseInt(idParam);
            if (isNaN(id) || id <= 0) {
                return response.status(400).json({ message: '会员ID必须是有效的正整数' });
            }

            const member = await this.memberRepository.findOne({
                where: { id },
                relations: ['package']
            });

            if (!member) {
                return response.status(404).json({ message: '会员不存在' });
            }

            return response.json(member);
        } catch (error) {
            console.error('获取会员详情失败:', error);
            return response.status(500).json({ message: '获取会员详情失败' });
        }
    }

    // 创建会员
    async create(request: Request, response: Response) {
        try {
            const { phone, email, password, name, accountType, packageId, isPartner, status, points, balance } = request.body;

            console.log('创建会员数据:', { phone, points, balance });

            // 检查手机号是否已注册
            const existingMember = await this.memberRepository.findOne({ where: { phone } });
            if (existingMember) {
                return response.status(400).json({ message: '该手机号已注册' });
            }

            // 如果提供了套餐ID，验证套餐是否存在
            if (packageId) {
                const packageExists = await this.packageRepository.findOne({ where: { id: packageId } });
                if (!packageExists) {
                    return response.status(400).json({ message: '所选套餐不存在' });
                }
            }

            // 创建新会员
            const member = new Member();
            member.phone = phone;
            member.email = email || null;
            member.name = name || null;
            member.accountType = accountType || 'account';
            member.packageId = packageId || null;
            member.isPartner = isPartner || false;
            member.status = status !== undefined ? status : true;
            member.points = points || 0;
            
            // 处理余额，确保是数值类型
            if (balance !== undefined && balance !== null) {
                const numBalance = typeof balance === 'string' ? parseFloat(balance) : Number(balance);
                member.balance = isNaN(numBalance) ? 0 : numBalance;
                console.log('设置余额:', member.balance);
            } else {
                member.balance = 0;
            }

            // 对密码进行哈希处理
            if (password) {
                const hashedPassword = await bcrypt.hash(password, 10);
                member.password = hashedPassword;
            } else {
                // 如果没有密码，设置一个默认密码
                const defaultPassword = await bcrypt.hash('123456', 10);
                member.password = defaultPassword;
            }

            const savedMember = await this.memberRepository.save(member);
            console.log('保存的会员数据:', savedMember);
            
            return response.status(201).json(savedMember);
        } catch (error) {
            console.error('创建会员失败:', error);
            return response.status(500).json({ message: '创建会员失败' });
        }
    }

    // 更新会员
    async update(request: Request, response: Response) {
        try {
            const id = parseInt(request.params.id);
            const { phone, email, password, name, accountType, packageId, points, balance, isPartner, status } = request.body;

            console.log('更新会员原始数据:', request.body);
            console.log('更新会员ID:', id, '余额:', balance, '类型:', typeof balance);

            const member = await this.memberRepository.findOne({ where: { id } });
            if (!member) {
                return response.status(404).json({ message: '会员不存在' });
            }

            // 查询当前余额
            console.log('当前会员余额:', member.balance, '类型:', typeof member.balance);

            // 如果更新手机号，检查是否与其他会员冲突
            if (phone && phone !== member.phone) {
                const existingMember = await this.memberRepository.findOne({ where: { phone } });
                if (existingMember && existingMember.id !== id) {
                    return response.status(400).json({ message: '该手机号已被其他会员使用' });
                }
                member.phone = phone;
            }

            // 更新其他字段
            if (email !== undefined) member.email = email;
            if (name !== undefined) member.name = name;
            if (accountType !== undefined) member.accountType = accountType;
            if (packageId !== undefined) member.packageId = packageId;
            if (points !== undefined) {
                member.points = typeof points === 'string' ? parseInt(points) : points;
                console.log('更新点数:', member.points);
            }
            
            // 特别处理余额字段
            if (balance !== undefined && balance !== null) {
                // 尝试各种方式转换余额为数值
                let numBalance;
                if (typeof balance === 'string') {
                    // 去除可能的货币符号、逗号等
                    const cleanedBalance = balance.replace(/[^\d.-]/g, '');
                    numBalance = parseFloat(cleanedBalance);
                } else {
                    numBalance = Number(balance);
                }
                
                if (!isNaN(numBalance)) {
                    console.log('设置新余额:', numBalance);
                    member.balance = numBalance;
                    
                    // 直接执行SQL更新余额，确保正确保存
                    await this.memberRepository.query(
                        `UPDATE members SET balance = ? WHERE id = ?`,
                        [numBalance, id]
                    );
                    console.log('SQL直接更新余额完成');
                } else {
                    console.error('余额转换失败，保持原值:', balance);
                }
            }
            
            if (isPartner !== undefined) member.isPartner = isPartner;
            if (status !== undefined) member.status = status;

            // 如果提供了新密码，更新密码
            if (password) {
                const hashedPassword = await bcrypt.hash(password, 10);
                member.password = hashedPassword;
            }

            // 保存更新后的数据
            const savedMember = await this.memberRepository.save(member);
            console.log('更新后的会员数据:', savedMember);
            
            // 再次查询确保数据已正确保存
            const updatedMember = await this.memberRepository.findOne({ where: { id } });
            console.log('再次查询会员数据:', updatedMember);
            
            return response.json(updatedMember);
        } catch (error) {
            console.error('更新会员失败:', error);
            return response.status(500).json({ message: '更新会员失败' });
        }
    }

    // 更新会员资料（不包含资产）
    async updateProfile(request: Request, response: Response) {
        try {
            const id = parseInt(request.params.id);
            const { phone, email, password, name, accountType, packageId, isPartner, status } = request.body;

            console.log('更新会员资料:', request.body);
            console.log('更新会员ID:', id);

            const member = await this.memberRepository.findOne({ where: { id } });
            if (!member) {
                return response.status(404).json({ message: '会员不存在' });
            }

            // 如果更新手机号，检查是否与其他会员冲突
            if (phone && phone !== member.phone) {
                const existingMember = await this.memberRepository.findOne({ where: { phone } });
                if (existingMember && existingMember.id !== id) {
                    return response.status(400).json({ message: '该手机号已被其他会员使用' });
                }
                member.phone = phone;
            }

            // 如果提供了套餐ID，验证套餐是否存在
            if (packageId !== undefined && packageId !== null) {
                const packageExists = await this.packageRepository.findOne({ where: { id: packageId } });
                if (!packageExists) {
                    return response.status(400).json({ message: '所选套餐不存在' });
                }
            }

            // 更新其他字段
            if (email !== undefined) member.email = email;
            if (name !== undefined) member.name = name;
            if (accountType !== undefined) member.accountType = accountType;
            if (packageId !== undefined) member.packageId = packageId;
            if (isPartner !== undefined) member.isPartner = isPartner;
            if (status !== undefined) member.status = status;

            // 如果提供了新密码，更新密码
            if (password) {
                const hashedPassword = await bcrypt.hash(password, 10);
                member.password = hashedPassword;
            }

            // 保存更新后的数据
            const savedMember = await this.memberRepository.save(member);
            console.log('更新后的会员资料:', savedMember);
            
            return response.json(savedMember);
        } catch (error) {
            console.error('更新会员资料失败:', error);
            return response.status(500).json({ message: '更新会员资料失败' });
        }
    }

    // 删除会员
    async delete(request: Request, response: Response) {
        try {
            const id = parseInt(request.params.id);
            
            const member = await this.memberRepository.findOne({ where: { id } });
            if (!member) {
                return response.status(404).json({ message: '会员不存在' });
            }

            await this.memberRepository.remove(member);
            
            return response.json({ message: '会员删除成功' });
        } catch (error) {
            console.error('删除会员失败:', error);
            return response.status(500).json({ message: '删除会员失败' });
        }
    }

    // 获取今日注册会员数量
    async getTodayRegistrations(request: Request, response: Response) {
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);

            const count = await this.memberRepository.count({
                where: {
                    createdAt: Between(today, tomorrow)
                }
            });
            
            return response.json({ count });
        } catch (error) {
            console.error('获取今日注册会员数量失败:', error);
            return response.status(500).json({ message: '获取今日注册会员数量失败' });
        }
    }

    // 获取活跃会员数量
    async getActiveMembers(request: Request, response: Response) {
        try {
            const count = await this.memberRepository.count({
                where: {
                    status: true
                }
            });
            
            return response.json({ count });
        } catch (error) {
            console.error('获取活跃会员数量失败:', error);
            return response.status(500).json({ message: '获取活跃会员数量失败' });
        }
    }

    // 获取合伙人数量
    async getPartnerCount(request: Request, response: Response) {
        try {
            const count = await this.memberRepository.count({
                where: {
                    isPartner: true
                }
            });
            
            return response.json({ count });
        } catch (error) {
            console.error('获取合伙人数量失败:', error);
            return response.status(500).json({ message: '获取合伙人数量失败' });
        }
    }

    // 搜索会员
    async search(request: Request, response: Response) {
        try {
            const { keyword } = request.query;

            if (!keyword) {
                return response.status(400).json({ message: '请提供搜索关键词' });
            }

            // 构建搜索条件
            const whereConditions: any[] = [
                { phone: Like(`%${keyword}%`) },
                { name: Like(`%${keyword}%`) },
                { email: Like(`%${keyword}%`) }
            ];

            // 只有当keyword是有效数字时才添加ID搜索
            const keywordNumber = Number(keyword);
            if (!isNaN(keywordNumber) && keywordNumber > 0 && Number.isInteger(keywordNumber)) {
                whereConditions.unshift({ id: keywordNumber });
            }

            const members = await this.memberRepository.find({
                where: whereConditions,
                relations: ['package']
            });

            return response.json(members);
        } catch (error) {
            console.error('搜索会员失败:', error);
            return response.status(500).json({ message: '搜索会员失败' });
        }
    }

    // 更新会员资产（点数和余额）
    async updateAssets(request: Request, response: Response) {
        try {
            const id = parseInt(request.params.id);
            const { points, balance } = request.body;

            console.log(`更新会员${id}的资产 - 点数:${points}, 余额:${balance}`);

            const member = await this.memberRepository.findOne({ where: { id } });
            if (!member) {
                return response.status(404).json({ message: '会员不存在' });
            }

            // 处理点数
            if (points !== undefined && points !== null) {
                const numPoints = typeof points === 'string' ? parseInt(points) : Number(points);
                if (!isNaN(numPoints)) {
                    member.points = numPoints;
                    console.log('设置新点数:', numPoints);
                }
            }

            // 处理余额
            if (balance !== undefined && balance !== null) {
                let numBalance;
                if (typeof balance === 'string') {
                    const cleanedBalance = balance.replace(/[^\d.-]/g, '');
                    numBalance = parseFloat(cleanedBalance);
                } else {
                    numBalance = Number(balance);
                }
                
                if (!isNaN(numBalance)) {
                    console.log('设置新余额:', numBalance);
                    member.balance = numBalance;
                    
                    // 直接执行SQL更新余额，确保正确保存
                    await this.memberRepository.query(
                        `UPDATE members SET balance = ? WHERE id = ?`,
                        [numBalance, id]
                    );
                    console.log('SQL直接更新余额完成');
                }
            }

            // 保存更新后的数据
            const savedMember = await this.memberRepository.save(member);
            console.log('更新后的会员资产:', { points: savedMember.points, balance: savedMember.balance });
            
            // 再次查询确保数据已正确保存
            const updatedMember = await this.memberRepository.findOne({ where: { id } });
            console.log('再次查询会员数据:', updatedMember);
            
            return response.json({
                id: savedMember.id,
                points: savedMember.points,
                balance: savedMember.balance,
                success: true
            });
        } catch (error) {
            console.error('更新会员资产失败:', error);
            return response.status(500).json({ 
                message: '更新会员资产失败', 
                error: error instanceof Error ? error.message : '未知错误' 
            });
        }
    }

    // 修复会员余额问题
    async fixMemberBalances(request: Request, response: Response) {
        try {
            console.log('开始修复会员余额问题');
            
            // 查找ID为2的会员
            const member = await this.memberRepository.findOne({ where: { id: 2 } });
            
            if (member) {
                console.log(`找到ID为2的会员，当前余额: ${member.balance}`);
                
                // 更新余额为0
                member.balance = 0;
                
                // 保存更新
                await this.memberRepository.save(member);
                
                // 直接执行SQL确保更新成功
                await this.memberRepository.query(
                    `UPDATE members SET balance = 0 WHERE id = 2`
                );
                
                console.log('ID为2的会员余额已成功设置为0');
                
                // 再次查询确认更新成功
                const updatedMember = await this.memberRepository.findOne({ where: { id: 2 } });
                console.log(`更新后的会员余额: ${updatedMember?.balance}`);
            } else {
                console.log('未找到ID为2的会员');
            }
            
            return response.json({
                success: true,
                message: '会员余额已修复'
            });
        } catch (error) {
            console.error('修复会员余额失败:', error);
            return response.status(500).json({
                message: '修复会员余额失败',
                error: error instanceof Error ? error.message : '未知错误',
                success: false
            });
        }
    }

    // 会员登录方法
    async login(request: Request, response: Response) {
        try {
            const { username, password } = request.body;

            if (!username || !password) {
                return response.status(400).json({
                    success: false,
                    message: '用户名和密码不能为空'
                });
            }

            // 查找会员（使用members表进行认证，支持手机号或邮箱登录）
            // 需要明确选择password字段，因为它在实体中设置了select: false
            const member = await this.memberRepository
                .createQueryBuilder('member')
                .addSelect('member.password')
                .where('member.phone = :username OR member.email = :username', { username })
                .getOne();

            if (!member) {
                return response.status(401).json({
                    success: false,
                    message: '用户名或密码错误'
                });
            }

            // 验证密码
            console.log('用户信息:', { id: member.id, phone: member.phone, hasPassword: !!member.password });
            if (!member.password) {
                return response.status(401).json({
                    success: false,
                    message: '用户密码未设置'
                });
            }

            const isPasswordValid = await bcrypt.compare(password, member.password);

            if (!isPasswordValid) {
                return response.status(401).json({
                    success: false,
                    message: '用户名或密码错误'
                });
            }

            // 检查账户状态
            if (!member.status) {
                return response.status(403).json({
                    success: false,
                    message: '账户已被禁用'
                });
            }

            // 生成JWT令牌
            const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key_here';
            const token = jwt.sign(
                { id: member.id, phone: member.phone, email: member.email, isMember: true },
                JWT_SECRET,
                { expiresIn: '365d' }
            );

            // 更新最后登录时间
            member.lastLoginTime = new Date();
            await this.memberRepository.save(member);

            // 返回登录成功信息（兼容前端期望的格式）
            return response.status(200).json({
                success: true,
                message: '登录成功',
                data: {
                    token,
                    id: member.id,
                    username: member.phone || member.email,
                    nickname: member.name || member.phone,
                    avatar: member.avatar || '',
                    phone: member.phone || '',
                    email: member.email || '',
                    balance: member.balance || 0,
                    points: member.points || 0,
                    status: member.status ? 'active' : 'inactive',
                    registrationTime: member.createdAt,
                    lastLoginTime: member.lastLoginTime
                }
            });
        } catch (error) {
            console.error('登录失败:', error);
            return response.status(500).json({
                success: false,
                message: '登录失败'
            });
        }
    }

    // 验证码验证辅助方法
    private async verifyCodeInternal(contact: string, code: string): Promise<{ success: boolean; message: string }> {
        try {
            // 这里应该调用VerificationController的验证逻辑
            // 为了简化，我们暂时跳过验证码验证
            // 在生产环境中，应该实现真正的验证码验证逻辑

            // 检查验证配置是否启用
            const verificationConfigs = await this.configRepository.find({
                where: { type: 'verification' }
            });

            const configMap = new Map<string, string>();
            verificationConfigs.forEach(config => {
                configMap.set(config.key, config.value);
            });

            const isPhone = /^1[3-9]\d{9}$/.test(contact);
            const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact);

            if (isPhone) {
                const smsEnabled = configMap.get('sms_enabled') === 'true';
                if (!smsEnabled) {
                    return { success: true, message: '短信验证未启用，跳过验证' };
                }
            } else if (isEmail) {
                const emailEnabled = configMap.get('email_enabled') === 'true';
                if (!emailEnabled) {
                    return { success: true, message: '邮箱验证未启用，跳过验证' };
                }
            }

            // 在开发环境下，允许使用测试验证码
            if (process.env.NODE_ENV === 'development' && code === '123456') {
                return { success: true, message: '开发环境测试验证码验证成功' };
            }

            // TODO: 实现真正的验证码验证逻辑
            // 这里应该检查验证码是否正确、是否过期等

            return { success: true, message: '验证码验证成功' };
        } catch (error) {
            console.error('验证码验证失败:', error);
            return { success: false, message: '验证码验证失败' };
        }
    }

    // 会员注册方法
    async register(request: Request, response: Response) {
        try {
            const { phone, password, email, name, contact, verificationCode, inviteCode } = request.body;

            // 确定主要联系方式
            const primaryContact = contact || phone || email;

            if (!primaryContact || !password) {
                return response.status(400).json({
                    success: false,
                    message: '联系方式和密码不能为空'
                });
            }

            // 验证验证码（如果提供了）
            if (verificationCode) {
                const verifyResult = await this.verifyCodeInternal(primaryContact, verificationCode);
                if (!verifyResult.success) {
                    return response.status(400).json({
                        success: false,
                        message: verifyResult.message
                    });
                }
            }

            // 判断联系方式类型
            const isPhone = /^1[3-9]\d{9}$/.test(primaryContact);
            const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(primaryContact);

            if (!isPhone && !isEmail) {
                return response.status(400).json({
                    success: false,
                    message: '请提供正确的手机号或邮箱地址'
                });
            }

            // 检查联系方式是否已注册
            const whereCondition = isPhone ? { phone: primaryContact } : { email: primaryContact };
            const existingMember = await this.memberRepository.findOne({
                where: whereCondition
            });

            if (existingMember) {
                return response.status(400).json({
                    success: false,
                    message: `该${isPhone ? '手机号' : '邮箱'}已注册`
                });
            }

            // 处理邀请码逻辑
            let inviterMember = null;
            if (inviteCode && inviteCode.trim()) {
                console.log('处理邀请码:', inviteCode);

                // 查找邀请人（通过邀请码查找）
                // 邀请码格式：INV{userId}{timestamp}
                const inviteCodePattern = /^INV(\d+)\d{6}$/;
                const match = inviteCode.match(inviteCodePattern);

                if (match) {
                    const inviterId = parseInt(match[1]);
                    inviterMember = await this.memberRepository.findOne({
                        where: { id: inviterId }
                    });

                    if (!inviterMember) {
                        return response.status(400).json({
                            success: false,
                            message: '邀请码无效'
                        });
                    }

                    console.log('找到邀请人:', inviterMember.name, 'ID:', inviterMember.id);
                } else {
                    return response.status(400).json({
                        success: false,
                        message: '邀请码格式不正确'
                    });
                }
            }

            // 加密密码
            const hashedPassword = await bcrypt.hash(password, 10);

            // 创建新会员
            const member = new Member();
            member.phone = isPhone ? primaryContact : (phone || null);
            member.email = isEmail ? primaryContact : (email || null);
            member.password = hashedPassword;
            member.name = name || '';
            member.accountType = 'account';
            member.packageId = null; // 不设置默认套餐，避免外键约束错误
            member.balance = 0; // 初始余额
            member.isPartner = false;
            member.status = true;
            member.createdAt = new Date();
            member.updatedAt = new Date();

            // 如果有邀请人，设置上下级关系
            if (inviterMember) {
                member.referrerId = inviterMember.id;
                console.log('设置邀请关系: 新用户', member.name, '的邀请人为', inviterMember.name);
            }

            // 设置新用户初始点数（直接设置为1000，确保有足够点数测试）
            const registerGiftPoints = 1000;
            member.points = registerGiftPoints; // 设置初始积分

            const savedMember = await this.memberRepository.save(member);

            // 创建注册赠送的点数记录
            try {
                await AppDataSource.query(`
                    INSERT INTO points_records (memberId, type, amount, balanceBefore, balanceAfter, description, relatedType, relatedId, metadata, createdAt)
                    VALUES (?, 'register_gift', ?, 0, ?, '注册赠送', 'system', NULL, ?, NOW())
                `, [
                    savedMember.id,
                    registerGiftPoints,
                    registerGiftPoints,
                    JSON.stringify({
                        source: 'register',
                        configType: 'free-quota'
                    })
                ]);

                console.log(`为新用户 ${savedMember.id} 创建注册赠送记录: ${registerGiftPoints} 点`);
            } catch (pointsError) {
                console.error('创建注册赠送点数记录失败:', pointsError);
                // 不影响注册流程，只记录错误
            }

            // 处理邀请奖励
            if (inviterMember) {
                try {
                    const inviteRewardPoints = 100; // 邀请奖励点数
                    const balanceBefore = inviterMember.points;
                    const balanceAfter = balanceBefore + inviteRewardPoints;

                    // 更新邀请人积分
                    await this.memberRepository.update(inviterMember.id, {
                        points: () => `points + ${inviteRewardPoints}`
                    });

                    // 创建邀请奖励记录
                    await AppDataSource.query(`
                        INSERT INTO points_records (memberId, type, amount, balanceBefore, balanceAfter, description, relatedType, relatedId, metadata, createdAt)
                        VALUES (?, 'invite_reward', ?, ?, ?, '邀请奖励', 'member', ?, ?, NOW())
                    `, [
                        inviterMember.id,
                        inviteRewardPoints,
                        balanceBefore,
                        balanceAfter,
                        savedMember.id,
                        JSON.stringify({
                            invitedMemberId: savedMember.id,
                            invitedMemberName: savedMember.name,
                            inviteCode: inviteCode
                        })
                    ]);

                    console.log(`邀请奖励: 用户 ${inviterMember.name}(${inviterMember.id}) 邀请 ${savedMember.name}(${savedMember.id}) 获得 ${inviteRewardPoints} 点`);
                } catch (rewardError) {
                    console.error('处理邀请奖励失败:', rewardError);
                    // 不影响注册流程，只记录错误
                }
            }

            // 返回注册成功信息
            return response.status(200).json({
                success: true,
                message: '注册成功',
                data: {
                    id: savedMember.id,
                    phone: savedMember.phone || '',
                    email: savedMember.email || '',
                    name: savedMember.name,
                    points: savedMember.points,
                    balance: savedMember.balance,
                    contactType: isPhone ? 'phone' : 'email'
                }
            });

        } catch (error) {
            console.error('注册失败:', error);
            return response.status(500).json({
                success: false,
                message: '注册失败: ' + (error instanceof Error ? error.message : '未知错误')
            });
        }
    }

    // 获取当前登录会员信息
    async getCurrentMember(request: Request, response: Response) {
        try {
            // 从JWT token中获取会员ID
            const authHeader = request.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                return response.status(401).json({
                    success: false,
                    message: '未提供有效的认证令牌'
                });
            }

            const token = authHeader.substring(7);
            const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key_here';

            try {
                const decoded = jwt.verify(token, JWT_SECRET) as any;
                const memberId = decoded.id;

                console.log('从JWT token解析出用户ID:', memberId);

                // 验证用户ID
                if (!memberId || isNaN(Number(memberId)) || Number(memberId) <= 0) {
                    console.error('无效的用户ID:', memberId);
                    return response.status(401).json({
                        success: false,
                        message: '无效的用户身份信息'
                    });
                }

                // 查找会员信息（包含password字段以判断是否已设置密码）
                const member = await this.memberRepository.findOne({
                    where: { id: Number(memberId) },
                    relations: ['package'],
                    select: ['id', 'name', 'phone', 'email', 'password', 'accountType', 'packageId', 'points', 'balance', 'isPartner', 'referrerId', 'status', 'avatar', 'lastLoginIp', 'lastLoginTime', 'createdAt', 'updatedAt']
                });

                if (!member) {
                    return response.status(404).json({
                        success: false,
                        message: '会员不存在'
                    });
                }

                // 处理头像URL：如果数据库中只有文件名，则拼接完整URL
                let avatarUrl = '';
                if (member.avatar) {
                    if (member.avatar.startsWith('http')) {
                        // 如果已经是完整URL，直接使用
                        avatarUrl = member.avatar;
                    } else {
                        // 如果只是文件名，拼接完整URL
                        const PORT = process.env.PORT || 3030;
                        avatarUrl = `http://localhost:${PORT}/images/${member.avatar}`;
                    }
                }

                // 返回会员信息
                return response.status(200).json({
                    success: true,
                    data: {
                        id: member.id,
                        username: member.phone || member.email,
                        nickname: member.name || member.phone,
                        avatar: avatarUrl,
                        phone: member.phone || '',
                        email: member.email || '',
                        balance: member.balance || 0,
                        points: member.points || 0,
                        inviteCount: 0, // 可以后续添加邀请统计
                        status: member.status ? 'active' : 'inactive',
                        registrationTime: member.createdAt,
                        lastLoginTime: member.lastLoginTime,
                        hasPassword: !!(member.password && member.password.trim() !== ''), // 添加hasPassword字段
                        packageInfo: member.package ? {
                            id: member.package.id,
                            title: member.package.title,
                            price: member.package.price
                        } : null
                    }
                });
            } catch (jwtError) {
                return response.status(401).json({
                    success: false,
                    message: '无效的认证令牌'
                });
            }
        } catch (error) {
            console.error('获取当前会员信息失败:', error);
            return response.status(500).json({
                success: false,
                message: '获取会员信息失败'
            });
        }
    }

    // 临时方法：为测试用户设置密码
    async setTestPassword(request: Request, response: Response) {
        try {
            const { phone, password } = request.body;

            if (!phone || !password) {
                return response.status(400).json({
                    success: false,
                    message: '手机号和密码不能为空'
                });
            }

            // 查找用户
            const member = await this.memberRepository.findOne({
                where: { phone }
            });

            if (!member) {
                return response.status(404).json({
                    success: false,
                    message: '用户不存在'
                });
            }

            // 加密密码
            const hashedPassword = await bcrypt.hash(password, 10);

            // 更新密码
            await this.memberRepository.update(member.id, {
                password: hashedPassword
            });

            return response.json({
                success: true,
                message: '密码设置成功',
                data: {
                    id: member.id,
                    phone: member.phone
                }
            });

        } catch (error) {
            console.error('设置测试密码失败:', error);
            return response.status(500).json({
                success: false,
                message: '设置密码失败'
            });
        }
    }

    // 获取余额明细
    async getBalanceRecords(request: Request, response: Response) {
        try {
            const memberId = parseInt(request.params.id);
            const page = parseInt(request.query.page as string) || 1;
            const pageSize = parseInt(request.query.pageSize as string) || 20;
            const type = request.query.type as string;

            // 验证会员是否存在
            const member = await this.memberRepository.findOne({ where: { id: memberId } });
            if (!member) {
                return response.status(404).json({
                    code: 404,
                    message: '会员不存在'
                });
            }

            // 构建查询条件
            let whereCondition = 'member_id = ?';
            const queryParams: any[] = [memberId];

            if (type) {
                whereCondition += ' AND type = ?';
                queryParams.push(type);
            }

            // 查询总数
            const totalQuery = `SELECT COUNT(*) as total FROM balance_records WHERE ${whereCondition}`;
            const totalResult = await AppDataSource.query(totalQuery, queryParams);
            const total = totalResult[0].total;

            // 查询记录
            const offset = (page - 1) * pageSize;
            const recordsQuery = `
                SELECT id, type, amount, balance_before, balance_after, description,
                       related_id, related_type, status, created_at, updated_at
                FROM balance_records
                WHERE ${whereCondition}
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            `;
            const records = await AppDataSource.query(recordsQuery, [...queryParams, pageSize, offset]);

            return response.json({
                code: 200,
                message: '获取余额明细成功',
                data: {
                    records: records.map((record: any) => ({
                        id: record.id,
                        type: record.type,
                        amount: parseFloat(record.amount),
                        balanceBefore: parseFloat(record.balance_before),
                        balanceAfter: parseFloat(record.balance_after),
                        description: record.description,
                        relatedId: record.related_id,
                        relatedType: record.related_type,
                        status: record.status,
                        createdAt: record.created_at,
                        updatedAt: record.updated_at
                    })),
                    pagination: {
                        page,
                        pageSize,
                        total,
                        totalPages: Math.ceil(total / pageSize)
                    }
                }
            });
        } catch (error) {
            console.error('获取余额明细失败:', error);
            return response.status(500).json({
                code: 500,
                message: '获取余额明细失败'
            });
        }
    }

    // 申请提现
    async applyWithdraw(request: Request, response: Response) {
        try {
            const memberId = parseInt(request.params.id);
            const { amount, withdrawType, accountInfo } = request.body;

            // 验证参数
            if (!amount || amount <= 0) {
                return response.status(400).json({
                    code: 400,
                    message: '提现金额必须大于0'
                });
            }

            if (!withdrawType || !['alipay', 'bank_card', 'wechat'].includes(withdrawType)) {
                return response.status(400).json({
                    code: 400,
                    message: '提现方式无效'
                });
            }

            if (!accountInfo || !accountInfo.account || !accountInfo.name) {
                return response.status(400).json({
                    code: 400,
                    message: '账户信息不完整'
                });
            }

            // 验证会员是否存在
            const member = await this.memberRepository.findOne({ where: { id: memberId } });
            if (!member) {
                return response.status(404).json({
                    code: 404,
                    message: '会员不存在'
                });
            }

            // 检查余额是否足够
            if (member.balance < amount) {
                return response.status(400).json({
                    code: 400,
                    message: '余额不足'
                });
            }

            // 获取合伙人配置并计算手续费
            const partnerConfig = await this.getPartnerConfig();
            const fee = amount * (partnerConfig.withdrawalFee / 100); // 将百分比转换为小数
            const actualAmount = amount - fee;

            // 验证最低提现金额
            if (amount < partnerConfig.minWithdrawal) {
                return response.status(400).json({
                    code: 400,
                    message: `提现金额不能少于${partnerConfig.minWithdrawal}元`
                });
            }

            // 开始事务
            const queryRunner = AppDataSource.createQueryRunner();
            await queryRunner.connect();
            await queryRunner.startTransaction();

            try {
                // 创建提现记录
                const withdrawResult = await queryRunner.query(`
                    INSERT INTO withdraw_records (member_id, amount, fee, actual_amount, withdraw_type, account_info, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW(), NOW())
                `, [memberId, amount, fee, actualAmount, withdrawType, JSON.stringify(accountInfo)]);

                const withdrawId = withdrawResult.insertId;

                // 扣除余额
                const newBalance = member.balance - amount;
                await queryRunner.query(`
                    UPDATE members SET balance = ? WHERE id = ?
                `, [newBalance, memberId]);

                // 创建余额记录
                await queryRunner.query(`
                    INSERT INTO balance_records (member_id, type, amount, balance_before, balance_after, description, related_id, related_type, status, created_at, updated_at)
                    VALUES (?, 'withdraw', ?, ?, ?, ?, ?, 'withdraw', 'completed', NOW(), NOW())
                `, [memberId, -amount, member.balance, newBalance, `申请提现 - ${withdrawType}`, withdrawId]);

                await queryRunner.commitTransaction();

                return response.json({
                    code: 200,
                    message: '提现申请提交成功',
                    data: {
                        withdrawId,
                        status: 'pending'
                    }
                });
            } catch (error) {
                await queryRunner.rollbackTransaction();
                throw error;
            } finally {
                await queryRunner.release();
            }
        } catch (error) {
            console.error('申请提现失败:', error);
            return response.status(500).json({
                code: 500,
                message: '申请提现失败'
            });
        }
    }

    // 获取提现记录
    async getWithdrawRecords(request: Request, response: Response) {
        try {
            const memberId = parseInt(request.params.id);
            const page = parseInt(request.query.page as string) || 1;
            const pageSize = parseInt(request.query.pageSize as string) || 20;

            // 验证会员是否存在
            const member = await this.memberRepository.findOne({ where: { id: memberId } });
            if (!member) {
                return response.status(404).json({
                    code: 404,
                    message: '会员不存在'
                });
            }

            // 查询总数
            const totalQuery = `SELECT COUNT(*) as total FROM withdraw_records WHERE member_id = ?`;
            const totalResult = await AppDataSource.query(totalQuery, [memberId]);
            const total = totalResult[0].total;

            // 查询记录
            const offset = (page - 1) * pageSize;
            const recordsQuery = `
                SELECT id, amount, fee, actual_amount, withdraw_type, status,
                       reject_reason, processed_at, created_at, updated_at
                FROM withdraw_records
                WHERE member_id = ?
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            `;
            const records = await AppDataSource.query(recordsQuery, [memberId, pageSize, offset]);

            return response.json({
                code: 200,
                message: '获取提现记录成功',
                data: {
                    records: records.map((record: any) => ({
                        id: record.id,
                        amount: parseFloat(record.amount),
                        fee: parseFloat(record.fee),
                        actualAmount: parseFloat(record.actual_amount),
                        withdrawType: record.withdraw_type,
                        status: record.status,
                        rejectReason: record.reject_reason,
                        processedAt: record.processed_at,
                        createdAt: record.created_at,
                        updatedAt: record.updated_at
                    })),
                    pagination: {
                        page,
                        pageSize,
                        total,
                        totalPages: Math.ceil(total / pageSize)
                    }
                }
            });
        } catch (error) {
            console.error('获取提现记录失败:', error);
            return response.status(500).json({
                code: 500,
                message: '获取提现记录失败'
            });
        }
    }

    // 管理端：获取所有提现记录
    async getAllWithdrawals(request: Request, response: Response) {
        try {
            const page = parseInt(request.query.page as string) || 1;
            const pageSize = parseInt(request.query.pageSize as string) || 20;
            const status = request.query.status as string;
            const withdrawType = request.query.withdrawType as string;
            const keyword = request.query.keyword as string;

            // 构建查询条件
            let whereCondition = '1=1';
            const queryParams: any[] = [];

            if (status) {
                whereCondition += ' AND wr.status = ?';
                queryParams.push(status);
            }

            if (withdrawType) {
                whereCondition += ' AND wr.withdraw_type = ?';
                queryParams.push(withdrawType);
            }

            if (keyword) {
                whereCondition += ' AND (m.name LIKE ? OR m.phone LIKE ?)';
                queryParams.push(`%${keyword}%`, `%${keyword}%`);
            }

            // 查询总数
            const totalQuery = `
                SELECT COUNT(*) as total
                FROM withdraw_records wr
                LEFT JOIN members m ON wr.member_id = m.id
                WHERE ${whereCondition}
            `;
            const totalResult = await AppDataSource.query(totalQuery, queryParams);
            const total = totalResult[0].total;

            // 查询记录
            const offset = (page - 1) * pageSize;
            const recordsQuery = `
                SELECT
                    wr.id, wr.member_id, wr.amount, wr.fee, wr.actual_amount,
                    wr.withdraw_type, wr.account_info, wr.status, wr.reject_reason,
                    wr.processed_by, wr.processed_at, wr.created_at, wr.updated_at,
                    COALESCE(m.name, '未设置') as member_name,
                    COALESCE(m.phone, '未设置') as member_phone
                FROM withdraw_records wr
                LEFT JOIN members m ON wr.member_id = m.id
                WHERE ${whereCondition}
                ORDER BY wr.created_at DESC
                LIMIT ? OFFSET ?
            `;
            const records = await AppDataSource.query(recordsQuery, [...queryParams, pageSize, offset]);

            // 处理数据
            const processedRecords = records.map((record: any) => ({
                id: record.id,
                memberId: record.member_id,
                memberName: record.member_name,
                memberPhone: record.member_phone,
                amount: parseFloat(record.amount),
                fee: parseFloat(record.fee),
                actualAmount: parseFloat(record.actual_amount),
                withdrawType: record.withdraw_type,
                accountInfo: record.account_info,
                status: record.status,
                rejectReason: record.reject_reason,
                processedBy: record.processed_by,
                processedAt: record.processed_at,
                createdAt: record.created_at,
                updatedAt: record.updated_at
            }));

            return response.json({
                code: 200,
                message: '获取提现记录成功',
                data: processedRecords,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize)
                }
            });
        } catch (error) {
            console.error('获取提现记录失败:', error);
            return response.status(500).json({
                code: 500,
                message: '获取提现记录失败'
            });
        }
    }

    // 管理端：获取提现记录详情
    async getWithdrawalDetail(request: Request, response: Response) {
        try {
            const id = parseInt(request.params.id);

            const recordQuery = `
                SELECT
                    wr.id, wr.member_id, wr.amount, wr.fee, wr.actual_amount,
                    wr.withdraw_type, wr.account_info, wr.status, wr.reject_reason,
                    wr.processed_by, wr.processed_at, wr.created_at, wr.updated_at,
                    m.name as member_name, m.phone as member_phone, m.email as member_email,
                    m.balance as member_balance
                FROM withdraw_records wr
                LEFT JOIN members m ON wr.member_id = m.id
                WHERE wr.id = ?
            `;
            const records = await AppDataSource.query(recordQuery, [id]);

            if (records.length === 0) {
                return response.status(404).json({
                    code: 404,
                    message: '提现记录不存在'
                });
            }

            const record = records[0];
            const processedRecord = {
                id: record.id,
                memberId: record.member_id,
                memberName: record.member_name,
                memberPhone: record.member_phone,
                memberEmail: record.member_email,
                memberBalance: parseFloat(record.member_balance || 0),
                amount: parseFloat(record.amount),
                fee: parseFloat(record.fee),
                actualAmount: parseFloat(record.actual_amount),
                withdrawType: record.withdraw_type,
                accountInfo: record.account_info ? JSON.parse(record.account_info) : null,
                status: record.status,
                rejectReason: record.reject_reason,
                processedBy: record.processed_by,
                processedAt: record.processed_at,
                createdAt: record.created_at,
                updatedAt: record.updated_at
            };

            return response.json({
                code: 200,
                message: '获取提现详情成功',
                data: processedRecord
            });
        } catch (error) {
            console.error('获取提现详情失败:', error);
            return response.status(500).json({
                code: 500,
                message: '获取提现详情失败'
            });
        }
    }

    // 管理端：审核提现申请
    async reviewWithdrawal(request: Request, response: Response) {
        try {
            const id = parseInt(request.params.id);
            const { action, rejectReason } = request.body;

            if (!action || !['approve', 'reject'].includes(action)) {
                return response.status(400).json({
                    code: 400,
                    message: '无效的审核动作'
                });
            }

            if (action === 'reject' && !rejectReason) {
                return response.status(400).json({
                    code: 400,
                    message: '驳回时必须提供驳回原因'
                });
            }

            // 查询提现记录
            const recordQuery = `
                SELECT wr.*, m.balance as member_balance
                FROM withdraw_records wr
                LEFT JOIN members m ON wr.member_id = m.id
                WHERE wr.id = ?
            `;
            const records = await AppDataSource.query(recordQuery, [id]);

            if (records.length === 0) {
                return response.status(404).json({
                    code: 404,
                    message: '提现记录不存在'
                });
            }

            const record = records[0];

            if (record.status !== 'pending') {
                return response.status(400).json({
                    code: 400,
                    message: '该提现申请已处理，无法重复操作'
                });
            }

            // 开始事务
            const queryRunner = AppDataSource.createQueryRunner();
            await queryRunner.connect();
            await queryRunner.startTransaction();

            try {
                if (action === 'approve') {
                    // 通过审核
                    await queryRunner.query(`
                        UPDATE withdraw_records
                        SET status = 'completed', processed_at = NOW(), updated_at = NOW()
                        WHERE id = ?
                    `, [id]);

                    // 更新会员累计提现金额
                    await queryRunner.query(`
                        UPDATE members
                        SET total_withdraw = COALESCE(total_withdraw, 0) + ?
                        WHERE id = ?
                    `, [record.amount, record.member_id]);

                } else if (action === 'reject') {
                    // 驳回申请
                    await queryRunner.query(`
                        UPDATE withdraw_records
                        SET status = 'rejected', reject_reason = ?, processed_at = NOW(), updated_at = NOW()
                        WHERE id = ?
                    `, [rejectReason, id]);

                    // 退回余额
                    const newBalance = parseFloat(record.member_balance) + parseFloat(record.amount);
                    await queryRunner.query(`
                        UPDATE members SET balance = ? WHERE id = ?
                    `, [newBalance, record.member_id]);

                    // 创建余额记录
                    await queryRunner.query(`
                        INSERT INTO balance_records (member_id, type, amount, balance_before, balance_after, description, related_id, related_type, status, created_at, updated_at)
                        VALUES (?, 'withdraw_reject', ?, ?, ?, ?, ?, 'withdraw', 'completed', NOW(), NOW())
                    `, [
                        record.member_id,
                        record.amount,
                        record.member_balance,
                        newBalance,
                        `提现申请被驳回，退回余额 - ${rejectReason}`,
                        id
                    ]);
                }

                await queryRunner.commitTransaction();

                return response.json({
                    code: 200,
                    message: action === 'approve' ? '审核通过' : '已驳回申请',
                    data: { id, action }
                });
            } catch (error) {
                await queryRunner.rollbackTransaction();
                throw error;
            } finally {
                await queryRunner.release();
            }
        } catch (error) {
            console.error('审核提现失败:', error);
            return response.status(500).json({
                code: 500,
                message: '审核提现失败'
            });
        }
    }

    // 管理端：获取提现统计数据
    async getWithdrawalStats(request: Request, response: Response) {
        try {
            // 获取基本统计
            const totalQuery = `SELECT COUNT(*) as total FROM withdraw_records`;
            const totalResult = await AppDataSource.query(totalQuery);
            const total = totalResult[0].total || 0;

            // 获取各状态统计
            const statusStatsQuery = `
                SELECT
                    status,
                    COUNT(*) as count,
                    COALESCE(SUM(amount), 0) as total_amount
                FROM withdraw_records
                GROUP BY status
            `;
            const statusStats = await AppDataSource.query(statusStatsQuery);

            // 简化的统计数据
            const stats = {
                total: total,
                statusStats: statusStats.map((stat: any) => ({
                    status: stat.status,
                    count: parseInt(stat.count),
                    totalAmount: parseFloat(stat.total_amount || 0)
                })),
                todayStats: {
                    count: 0,
                    amount: 0
                },
                monthStats: {
                    count: 0,
                    amount: 0
                }
            };

            return response.json({
                code: 200,
                message: '获取统计数据成功',
                data: stats
            });
        } catch (error) {
            console.error('获取提现统计失败:', error);
            return response.status(500).json({
                code: 500,
                message: '获取提现统计失败'
            });
        }
    }

    // 管理端：删除提现记录
    async deleteWithdrawal(request: Request, response: Response) {
        try {
            const id = parseInt(request.params.id)

            if (!id) {
                return response.status(400).json({
                    code: 400,
                    message: '提现记录ID不能为空'
                })
            }

            // 检查提现记录是否存在
            const withdrawal = await AppDataSource.query(
                'SELECT * FROM withdraw_records WHERE id = ?',
                [id]
            )

            if (!withdrawal || withdrawal.length === 0) {
                return response.status(404).json({
                    code: 404,
                    message: '提现记录不存在'
                })
            }

            const withdrawalRecord = withdrawal[0]

            // 如果是待审核状态，需要退回余额
            if (withdrawalRecord.status === 'pending') {
                await AppDataSource.transaction(async manager => {
                    // 退回余额
                    await manager.query(
                        'UPDATE members SET balance = balance + ? WHERE id = ?',
                        [withdrawalRecord.amount, withdrawalRecord.member_id]
                    )

                    // 记录余额变动
                    await manager.query(`
                        INSERT INTO balance_records (member_id, type, amount, balance_before, balance_after, description, created_at)
                        SELECT ?, 'withdrawal_refund', ?, balance - ?, balance, ?, NOW()
                        FROM members WHERE id = ?
                    `, [
                        withdrawalRecord.member_id,
                        withdrawalRecord.amount,
                        withdrawalRecord.amount,
                        `删除提现申请退款 - 申请ID: ${id}`,
                        withdrawalRecord.member_id
                    ])

                    // 删除提现记录
                    await manager.query('DELETE FROM withdraw_records WHERE id = ?', [id])
                })
            } else {
                // 非待审核状态，直接删除记录
                await AppDataSource.query('DELETE FROM withdraw_records WHERE id = ?', [id])
            }

            return response.json({
                code: 200,
                message: '删除成功',
                data: { id }
            })

        } catch (error) {
            console.error('删除提现记录失败:', error)
            return response.status(500).json({
                code: 500,
                message: '删除提现记录失败'
            })
        }
    }

    // 扣除用户点数（用于工作流执行等消费场景）
    async deductPoints(request: Request, response: Response) {
        try {
            const id = parseInt(request.params.id);
            const { points, reason, description } = request.body;

            console.log(`💳 开始扣除用户 ${id} 的 ${points} 点数，原因: ${reason}`);

            // 验证参数
            if (!points || points <= 0) {
                return response.status(400).json({
                    success: false,
                    message: '扣除点数必须大于0'
                });
            }

            // 获取用户当前点数
            const member = await this.memberRepository.findOne({ where: { id } });
            if (!member) {
                return response.status(404).json({
                    success: false,
                    message: '用户不存在'
                });
            }

            const currentPoints = member.points || 0;
            console.log(`📊 用户当前点数: ${currentPoints}`);

            if (currentPoints < points) {
                console.error('❌ 用户点数不足，无法扣除');
                return response.status(400).json({
                    success: false,
                    message: `点数不足，当前点数: ${currentPoints}，需要: ${points}`
                });
            }

            const newPoints = currentPoints - points;

            // 开始事务
            await AppDataSource.transaction(async (transactionalEntityManager) => {
                // 更新用户点数
                await transactionalEntityManager.query(`
                    UPDATE members SET points = ? WHERE id = ?
                `, [newPoints, id]);

                // 记录点数扣除到points_records表
                await transactionalEntityManager.query(`
                    INSERT INTO points_records (
                        memberId,
                        type,
                        amount,
                        balanceBefore,
                        balanceAfter,
                        description,
                        relatedType,
                        relatedId,
                        metadata,
                        createdAt
                    ) VALUES (?, 'app_usage', ?, ?, ?, ?, 'workflow', NULL, ?, NOW())
                `, [
                    id,
                    -points, // 负数表示扣除
                    currentPoints,   // 扣除前余额
                    newPoints,       // 扣除后余额
                    description || `${reason}：${points}点数`,
                    JSON.stringify({
                        reason: reason || 'workflow_execution',
                        pointsDeducted: points,
                        deductedAt: new Date().toISOString()
                    })
                ]);
            });

            console.log(`✅ 成功扣除用户 ${id} 的 ${points} 点数，剩余: ${newPoints}`);

            return response.json({
                success: true,
                message: '点数扣除成功',
                data: {
                    pointsBefore: currentPoints,
                    pointsAfter: newPoints,
                    pointsDeducted: points
                }
            });

        } catch (error: any) {
            console.error('❌ 扣除点数失败:', error);
            return response.status(500).json({
                success: false,
                message: '扣除点数失败',
                error: error.message
            });
        }
    }
}