import { defineStore } from 'pinia';
import axios from 'axios';

interface LogoConfig {
  mainLogo: string;
  sidebarLogo: string;
  miniLogo: string;
  userLogo: string;
  favicon: string;
  customerServiceImage: string;
  primaryColor: string;
}

interface BasicConfig {
  siteName: string;
  siteDescription: string;
  adminEmail: string;
  copyright: string;
  beianInfo: string;
  siteUrl: string;
}

export const useConfigStore = defineStore('config', {
  state: () => ({
    logoConfig: {
      mainLogo: '',
      sidebarLogo: '',
      miniLogo: '',
      userLogo: '',
      favicon: '',
      customerServiceImage: '',
      primaryColor: '#1890ff'
    } as LogoConfig,
    basicConfig: {
      siteName: 'AI智能体管理系统',
      siteDescription: '专业的AI智能体管理平台',
      adminEmail: '<EMAIL>',
      copyright: `© ${new Date().getFullYear()} AI智能体管理系统`,
      beianInfo: '',
      siteUrl: window.location.origin
    } as BasicConfig,
    isLoaded: false
  }),
  
  getters: {
    getLogoConfig(): LogoConfig {
      return this.logoConfig;
    },
    getBasicConfig(): BasicConfig {
      return this.basicConfig;
    }
  },
  
  actions: {
    async loadAllConfigs() {
      await Promise.all([
        this.loadLogoConfig(),
        this.loadBasicConfig()
      ]);
      this.isLoaded = true;
    },
    
    async loadLogoConfig() {
      try {
        if (import.meta.env.DEV) {
          console.debug('从store加载Logo配置...');
        }
        // 尝试从服务器状态文件读取正确的API URL
        let apiUrl = '/api'; // 默认使用相对路径
        try {
          const response = await fetch('/server-status.json');
          if (response.ok) {
            const serverStatus = await response.json();
            if (serverStatus && serverStatus.url) {
              apiUrl = `${serverStatus.url}/api`;
              if (import.meta.env.DEV) {
                console.debug('从服务器状态文件读取到API URL:', apiUrl);
              }
            }
          }
        } catch (e) {
          if (import.meta.env.DEV) {
            console.debug('读取服务器状态文件失败，使用默认API路径', e);
          }
        }
        
        // 获取认证令牌
        const token = localStorage.getItem('token');
        
        const response = await axios.get(`${apiUrl}/configs/system-logo`, {
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Authorization': token ? `Bearer ${token}` : ''
          }
        });
        
        if (response.data && response.data.code === 200 && response.data.data) {
          const configData = response.data.data;
          let configMap: Record<string, string> = {};

          // 处理后端返回的数据格式
          if (configData.configValue) {
            configMap = configData.configValue;
          } else if (Array.isArray(configData)) {
            // 如果是数组格式
            configData.forEach((item: any) => {
              configMap[item.key] = item.value;
            });
          }

          if (import.meta.env.DEV) {
            console.debug('获取到的Logo配置:', configMap);
          }

          // 更新状态
          this.logoConfig = {
            mainLogo: configMap.mainLogo || this.logoConfig.mainLogo,
            sidebarLogo: configMap.sidebarLogo || this.logoConfig.sidebarLogo,
            miniLogo: configMap.miniLogo || this.logoConfig.miniLogo,
            userLogo: configMap.userLogo || this.logoConfig.userLogo,
            favicon: configMap.favicon || this.logoConfig.favicon,
            primaryColor: configMap.primaryColor || this.logoConfig.primaryColor
          };
          
          // 应用主题色
          this.applyThemeColor(this.logoConfig.primaryColor);
          
          // 更新网站图标
          if (this.logoConfig.favicon) {
            this.applyFavicon(this.logoConfig.favicon);
          }
          
          if (import.meta.env.DEV) {
            console.debug('Logo配置已更新:', this.logoConfig);
          }
          
          // 触发全局事件
          window.dispatchEvent(new CustomEvent('logo-config-updated', {
            detail: { data: this.logoConfig }
          }));
          
          return true;
        }
        return false;
      } catch (error) {
        if (import.meta.env.DEV) {
          console.debug('加载Logo配置失败:', error);
        }
        return false;
      }
    },
    
    // 应用网站图标
    applyFavicon(url: string) {
      const faviconLink = document.querySelector('link[rel="icon"]') as HTMLLinkElement || document.createElement('link');
      faviconLink.type = 'image/x-icon';
      faviconLink.rel = 'icon';
      faviconLink.href = url;
      document.head.appendChild(faviconLink);
    },
    
    // 应用主题色
    applyThemeColor(color: string) {
      document.documentElement.style.setProperty('--primary-color', color);
      
      // 提取RGB值用于透明度变化
      const hexToRgb = (hex: string) => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16)
        } : null;
      };
      
      const rgb = hexToRgb(color);
      if (rgb) {
        const rgbValue = `${rgb.r}, ${rgb.g}, ${rgb.b}`;
        document.documentElement.style.setProperty('--primary-color-rgb', rgbValue);
      }
    },
    
    async loadBasicConfig() {
      try {
        if (import.meta.env.DEV) {
          console.debug('从store加载基础配置...');
        }
        // 尝试从服务器状态文件读取正确的API URL
        let apiUrl = '/api'; // 默认使用相对路径
        try {
          const response = await fetch('/server-status.json');
          if (response.ok) {
            const serverStatus = await response.json();
            if (serverStatus && serverStatus.url) {
              apiUrl = `${serverStatus.url}/api`;
              if (import.meta.env.DEV) {
                console.debug('从服务器状态文件读取到API URL:', apiUrl);
              }
            }
          }
        } catch (e) {
          if (import.meta.env.DEV) {
            console.debug('读取服务器状态文件失败，使用默认API路径', e);
          }
        }
        
        // 获取认证令牌
        const token = localStorage.getItem('token');
        
        const response = await axios.get(`${apiUrl}/configs/type/system-basic`, {
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Authorization': token ? `Bearer ${token}` : ''
          }
        });
        
        if (response.data && response.data.success && response.data.data) {
          const configMap: Record<string, string> = {};
          response.data.data.forEach((item: any) => {
            configMap[item.key] = item.value;
          });
          
          if (import.meta.env.DEV) {
            console.debug('获取到的基础配置:', configMap);
          }
          
          // 更新状态
          this.basicConfig = {
            siteName: configMap.siteName || this.basicConfig.siteName,
            siteDescription: configMap.siteDescription || this.basicConfig.siteDescription,
            adminEmail: configMap.adminEmail || this.basicConfig.adminEmail,
            copyright: configMap.copyright || this.basicConfig.copyright,
            beianInfo: configMap.beianInfo || this.basicConfig.beianInfo,
            siteUrl: configMap.siteUrl || this.basicConfig.siteUrl
          };
          
          // 更新页面标题
          if (this.basicConfig.siteName) {
            document.title = this.basicConfig.siteName;
          }
          
          if (import.meta.env.DEV) {
            console.debug('基础配置已更新:', this.basicConfig);
          }
          
          // 触发全局事件
          window.dispatchEvent(new CustomEvent('basic-config-updated', {
            detail: { data: this.basicConfig }
          }));
          
          return true;
        }
        return false;
      } catch (error) {
        if (import.meta.env.DEV) {
          console.debug('加载基础配置失败:', error);
        }
        return false;
      }
    }
  }
}); 